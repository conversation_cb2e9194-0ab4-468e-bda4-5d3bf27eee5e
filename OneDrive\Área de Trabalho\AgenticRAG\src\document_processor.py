"""
Processador de documentos para indexação no sistema RAG
"""

import os
import uuid
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import mimetypes
from dataclasses import dataclass

# Processamento de documentos
import PyPDF2
from docx import Document as DocxDocument
import openpyxl
import pandas as pd

from loguru import logger
from .hybrid_search import Document


@dataclass
class ProcessingConfig:
    """Configuração para processamento de documentos"""
    chunk_size: int = 1000
    chunk_overlap: int = 200
    min_chunk_size: int = 100
    max_chunk_size: int = 2000
    supported_extensions: List[str] = None
    
    def __post_init__(self):
        if self.supported_extensions is None:
            self.supported_extensions = [
                '.txt', '.pdf', '.docx', '.xlsx', '.csv', '.md'
            ]


class DocumentProcessor:
    """
    Processador de documentos que extrai texto e cria chunks para indexação
    """
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """
        Inicializa o processador de documentos
        
        Args:
            config: Configuração de processamento
        """
        self.config = config or ProcessingConfig()
        logger.info("DocumentProcessor inicializado")
    
    def process_file(self, file_path: Union[str, Path]) -> List[Document]:
        """
        Processa um arquivo e retorna lista de documentos
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            Lista de documentos processados
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            logger.error(f"Arquivo não encontrado: {file_path}")
            return []
        
        if not self._is_supported_file(file_path):
            logger.warning(f"Tipo de arquivo não suportado: {file_path}")
            return []
        
        logger.info(f"Processando arquivo: {file_path}")
        
        try:
            # Extrair texto do arquivo
            text_content = self._extract_text(file_path)
            
            if not text_content.strip():
                logger.warning(f"Nenhum texto extraído de: {file_path}")
                return []
            
            # Criar metadados
            metadata = self._create_metadata(file_path)
            
            # Dividir em chunks
            chunks = self._create_chunks(text_content)
            
            # Criar documentos
            documents = []
            for i, chunk in enumerate(chunks):
                doc_id = f"{file_path.stem}_{i}_{uuid.uuid4().hex[:8]}"
                
                chunk_metadata = metadata.copy()
                chunk_metadata.update({
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'chunk_size': len(chunk)
                })
                
                documents.append(Document(
                    id=doc_id,
                    content=chunk,
                    metadata=chunk_metadata
                ))
            
            logger.info(f"Arquivo processado: {len(documents)} chunks criados")
            return documents
            
        except Exception as e:
            logger.error(f"Erro ao processar arquivo {file_path}: {e}")
            return []
    
    def process_directory(
        self, 
        directory_path: Union[str, Path],
        recursive: bool = True
    ) -> List[Document]:
        """
        Processa todos os arquivos de um diretório
        
        Args:
            directory_path: Caminho para o diretório
            recursive: Se deve processar subdiretórios
            
        Returns:
            Lista de todos os documentos processados
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists() or not directory_path.is_dir():
            logger.error(f"Diretório não encontrado: {directory_path}")
            return []
        
        logger.info(f"Processando diretório: {directory_path} (recursive={recursive})")
        
        all_documents = []
        
        # Encontrar arquivos
        if recursive:
            files = directory_path.rglob("*")
        else:
            files = directory_path.glob("*")
        
        # Processar cada arquivo
        for file_path in files:
            if file_path.is_file() and self._is_supported_file(file_path):
                documents = self.process_file(file_path)
                all_documents.extend(documents)
        
        logger.info(f"Diretório processado: {len(all_documents)} documentos criados")
        return all_documents
    
    def process_text(
        self, 
        text: str, 
        source_name: str = "text_input",
        metadata: Optional[Dict[str, Any]] = None
    ) -> List[Document]:
        """
        Processa texto diretamente
        
        Args:
            text: Texto para processar
            source_name: Nome da fonte
            metadata: Metadados adicionais
            
        Returns:
            Lista de documentos processados
        """
        if not text.strip():
            logger.warning("Texto vazio fornecido")
            return []
        
        logger.info(f"Processando texto: {len(text)} caracteres")
        
        try:
            # Criar metadados base
            base_metadata = {
                'source': source_name,
                'source_type': 'text',
                'content_length': len(text)
            }
            
            if metadata:
                base_metadata.update(metadata)
            
            # Dividir em chunks
            chunks = self._create_chunks(text)
            
            # Criar documentos
            documents = []
            for i, chunk in enumerate(chunks):
                doc_id = f"{source_name}_{i}_{uuid.uuid4().hex[:8]}"
                
                chunk_metadata = base_metadata.copy()
                chunk_metadata.update({
                    'chunk_index': i,
                    'total_chunks': len(chunks),
                    'chunk_size': len(chunk)
                })
                
                documents.append(Document(
                    id=doc_id,
                    content=chunk,
                    metadata=chunk_metadata
                ))
            
            logger.info(f"Texto processado: {len(documents)} chunks criados")
            return documents
            
        except Exception as e:
            logger.error(f"Erro ao processar texto: {e}")
            return []
    
    def _is_supported_file(self, file_path: Path) -> bool:
        """Verifica se o arquivo é suportado"""
        return file_path.suffix.lower() in self.config.supported_extensions
    
    def _extract_text(self, file_path: Path) -> str:
        """
        Extrai texto de um arquivo baseado na extensão
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            Texto extraído
        """
        extension = file_path.suffix.lower()
        
        if extension == '.txt' or extension == '.md':
            return self._extract_text_from_txt(file_path)
        elif extension == '.pdf':
            return self._extract_text_from_pdf(file_path)
        elif extension == '.docx':
            return self._extract_text_from_docx(file_path)
        elif extension == '.xlsx':
            return self._extract_text_from_xlsx(file_path)
        elif extension == '.csv':
            return self._extract_text_from_csv(file_path)
        else:
            logger.warning(f"Extração não implementada para: {extension}")
            return ""
    
    def _extract_text_from_txt(self, file_path: Path) -> str:
        """Extrai texto de arquivo TXT/MD"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        except UnicodeDecodeError:
            # Tentar outras codificações
            for encoding in ['latin-1', 'cp1252']:
                try:
                    with open(file_path, 'r', encoding=encoding) as file:
                        return file.read()
                except UnicodeDecodeError:
                    continue
            logger.error(f"Não foi possível decodificar: {file_path}")
            return ""
    
    def _extract_text_from_pdf(self, file_path: Path) -> str:
        """Extrai texto de arquivo PDF"""
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto do PDF {file_path}: {e}")
            return ""
    
    def _extract_text_from_docx(self, file_path: Path) -> str:
        """Extrai texto de arquivo DOCX"""
        try:
            doc = DocxDocument(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto do DOCX {file_path}: {e}")
            return ""
    
    def _extract_text_from_xlsx(self, file_path: Path) -> str:
        """Extrai texto de arquivo XLSX"""
        try:
            workbook = openpyxl.load_workbook(file_path)
            text = ""
            
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                text += f"PLANILHA: {sheet_name}\n"
                
                for row in sheet.iter_rows(values_only=True):
                    row_text = "\t".join([str(cell) if cell is not None else "" for cell in row])
                    if row_text.strip():
                        text += row_text + "\n"
                text += "\n"
            
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto do XLSX {file_path}: {e}")
            return ""
    
    def _extract_text_from_csv(self, file_path: Path) -> str:
        """Extrai texto de arquivo CSV"""
        try:
            df = pd.read_csv(file_path)
            # Converter DataFrame para texto estruturado
            text = f"ARQUIVO CSV: {file_path.name}\n"
            text += f"COLUNAS: {', '.join(df.columns)}\n\n"
            
            for index, row in df.iterrows():
                row_text = "\t".join([str(value) for value in row.values])
                text += row_text + "\n"
            
            return text
        except Exception as e:
            logger.error(f"Erro ao extrair texto do CSV {file_path}: {e}")
            return ""
    
    def _create_metadata(self, file_path: Path) -> Dict[str, Any]:
        """
        Cria metadados para um arquivo
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            Dicionário com metadados
        """
        stat = file_path.stat()
        mime_type, _ = mimetypes.guess_type(str(file_path))
        
        return {
            'source': str(file_path),
            'filename': file_path.name,
            'extension': file_path.suffix.lower(),
            'size_bytes': stat.st_size,
            'mime_type': mime_type,
            'source_type': 'file'
        }
    
    def _create_chunks(self, text: str) -> List[str]:
        """
        Divide texto em chunks
        
        Args:
            text: Texto para dividir
            
        Returns:
            Lista de chunks
        """
        if len(text) <= self.config.chunk_size:
            return [text]
        
        chunks = []
        start = 0
        
        while start < len(text):
            # Calcular fim do chunk
            end = start + self.config.chunk_size
            
            # Se não é o último chunk, tentar quebrar em uma palavra
            if end < len(text):
                # Procurar por quebra de linha ou espaço
                for i in range(end, max(start + self.config.min_chunk_size, end - 100), -1):
                    if text[i] in ['\n', '.', '!', '?', ' ']:
                        end = i + 1
                        break
            
            # Extrair chunk
            chunk = text[start:end].strip()
            
            if len(chunk) >= self.config.min_chunk_size:
                chunks.append(chunk)
            
            # Calcular próximo início com overlap
            start = end - self.config.chunk_overlap
            
            # Evitar loop infinito
            if start >= end:
                start = end
        
        return chunks

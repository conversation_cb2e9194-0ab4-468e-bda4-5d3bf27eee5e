"""
Exemplo de cliente para a API do Agente RAG Híbrido
"""

import requests
import json
import time
from pathlib import Path


class RAGAPIClient:
    """Cliente para interagir com a API do Agente RAG"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        Inicializa o cliente da API
        
        Args:
            base_url: URL base da API
        """
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def health_check(self):
        """Verifica a saúde da API"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"❌ Erro ao verificar saúde da API: {e}")
            return None
    
    def get_stats(self):
        """Obtém estatísticas do agente"""
        try:
            response = self.session.get(f"{self.base_url}/stats")
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"❌ Erro ao obter estatísticas: {e}")
            return None
    
    def index_text(self, text: str, source_name: str = "api_client", metadata: dict = None):
        """
        Indexa texto via API
        
        Args:
            text: Texto para indexar
            source_name: Nome da fonte
            metadata: Metadados adicionais
        """
        try:
            data = {
                "text": text,
                "source_name": source_name,
                "metadata": metadata or {}
            }
            
            response = self.session.post(
                f"{self.base_url}/index/text",
                json=data
            )
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Erro ao indexar texto: {e}")
            return None
    
    def index_files(self, file_paths: list):
        """
        Indexa arquivos via API
        
        Args:
            file_paths: Lista de caminhos para arquivos
        """
        try:
            files = []
            for file_path in file_paths:
                path = Path(file_path)
                if path.exists():
                    files.append(('files', (path.name, open(path, 'rb'))))
                else:
                    print(f"⚠️  Arquivo não encontrado: {file_path}")
            
            if not files:
                print("❌ Nenhum arquivo válido para indexar")
                return None
            
            response = self.session.post(
                f"{self.base_url}/index/files",
                files=files
            )
            
            # Fechar arquivos
            for _, (_, file_obj) in files:
                file_obj.close()
            
            response.raise_for_status()
            return response.json()
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Erro ao indexar arquivos: {e}")
            return None
    
    def query(self, question: str, **kwargs):
        """
        Faz uma consulta ao agente
        
        Args:
            question: Pergunta para o agente
            **kwargs: Parâmetros adicionais (max_retrieved_docs, search_type, etc.)
        """
        try:
            data = {
                "question": question,
                **kwargs
            }
            
            start_time = time.time()
            response = self.session.post(
                f"{self.base_url}/query",
                json=data
            )
            response.raise_for_status()
            
            result = response.json()
            result['client_processing_time'] = time.time() - start_time
            
            return result
            
        except requests.exceptions.RequestException as e:
            print(f"❌ Erro na consulta: {e}")
            return None


def main():
    """Exemplo de uso do cliente da API"""
    
    print("🚀 Cliente da API do Agente RAG Híbrido")
    print("=" * 50)
    
    # Inicializar cliente
    client = RAGAPIClient()
    
    # Verificar se a API está funcionando
    print("\n🔍 Verificando saúde da API...")
    health = client.health_check()
    
    if not health:
        print("❌ API não está acessível. Certifique-se de que o servidor está rodando.")
        print("Execute: uvicorn src.api:app --host 0.0.0.0 --port 8000")
        return
    
    print(f"✅ API Status: {health['status']}")
    
    # Verificar componentes
    print("\n🧪 Status dos componentes:")
    for component, status in health['tests'].items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {component}")
    
    # Indexar texto de exemplo
    print("\n📚 Indexando texto de exemplo...")
    
    exemplo_texto = """
    O Processamento de Linguagem Natural (PLN) é uma área da inteligência artificial 
    que se concentra na interação entre computadores e linguagem humana. O objetivo 
    principal é permitir que os computadores compreendam, interpretem e gerem 
    linguagem humana de forma útil e significativa.
    
    As principais tarefas do PLN incluem:
    
    1. Análise Sintática: Compreender a estrutura gramatical das frases
    2. Análise Semântica: Extrair o significado das palavras e frases
    3. Análise Pragmática: Compreender o contexto e a intenção
    4. Geração de Texto: Produzir texto coerente e relevante
    5. Tradução Automática: Converter texto entre diferentes idiomas
    
    Aplicações modernas do PLN incluem assistentes virtuais, chatbots, 
    sistemas de recomendação, análise de sentimentos e muito mais.
    """
    
    result = client.index_text(
        text=exemplo_texto,
        source_name="exemplo_pln",
        metadata={"categoria": "educacional", "tema": "PLN"}
    )
    
    if result:
        print(f"✅ Texto indexado: {result['documents_indexed']} documentos")
    
    # Obter estatísticas
    print("\n📊 Estatísticas do sistema:")
    stats = client.get_stats()
    
    if stats:
        agent_stats = stats['agent_stats']
        search_stats = agent_stats['rag_system']['search_engine']
        
        print(f"  📄 Documentos indexados: {search_stats['total_documents']}")
        print(f"  🧠 Modelo: {agent_stats['rag_system']['llm_model']['name']}")
        print(f"  🔍 Busca padrão: {agent_stats['rag_system']['config']['search_type']}")
    
    # Fazer consultas de exemplo
    print("\n💬 Fazendo consultas de exemplo:")
    
    perguntas = [
        "O que é Processamento de Linguagem Natural?",
        "Quais são as principais tarefas do PLN?",
        "Cite aplicações modernas do PLN",
        "Como funciona a análise semântica?"
    ]
    
    for i, pergunta in enumerate(perguntas, 1):
        print(f"\n{i}. {pergunta}")
        
        result = client.query(pergunta)
        
        if result:
            print(f"   🤖 Resposta: {result['answer'][:200]}...")
            print(f"   📄 Documentos recuperados: {result['retrieved_documents']}")
            print(f"   ⏱️  Tempo: {result['processing_time']:.2f}s")
        else:
            print("   ❌ Erro na consulta")
    
    # Testar diferentes tipos de busca
    print("\n🔍 Testando diferentes tipos de busca:")
    pergunta_teste = "Explique análise sintática em PLN"
    
    tipos_busca = ["semantic", "lexical", "hybrid"]
    
    for tipo in tipos_busca:
        print(f"\n--- Busca {tipo.upper()} ---")
        result = client.query(
            pergunta_teste,
            search_type=tipo,
            max_retrieved_docs=3
        )
        
        if result:
            print(f"Resposta: {result['answer'][:150]}...")
            print(f"Docs: {result['retrieved_documents']}, Tempo: {result['processing_time']:.2f}s")
    
    # Exemplo com parâmetros personalizados
    print("\n⚙️  Consulta com parâmetros personalizados:")
    result = client.query(
        "Quais são os desafios do PLN?",
        max_retrieved_docs=2,
        search_type="hybrid",
        min_relevance_score=0.15
    )
    
    if result:
        print(f"Resposta personalizada: {result['answer']}")
        print(f"Configuração: {result['retrieved_documents']} docs, tipo: {result['search_type']}")
    
    print("\n✅ Exemplo do cliente API concluído!")


if __name__ == "__main__":
    main()

"""
Testes para o agente RAG híbrido
"""

import pytest
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path
from src.agent import HybridRAGAgent, AgentConfig
from src.rag_system import RAGConfig
from src.gemini_client import GeminiConfig
from src.document_processor import ProcessingConfig


class TestHybridRAGAgent:
    """Testes para HybridRAGAgent"""
    
    @pytest.fixture
    def mock_env_api_key(self, monkeypatch):
        """Mock da variável de ambiente API key"""
        monkeypatch.setenv("GOOGLE_API_KEY", "test_api_key")
    
    @pytest.fixture
    def agent_config(self):
        """Fixture para configuração do agente"""
        gemini_config = GeminiConfig(
            model_name="gemini-2.0-flash-exp",
            api_key="test_key"
        )
        
        rag_config = RAGConfig(gemini_config=gemini_config)
        processing_config = ProcessingConfig()
        
        return AgentConfig(
            rag_config=rag_config,
            processing_config=processing_config,
            agent_name="Agente Teste",
            use_reasoning=True
        )
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_initialization_with_config(self, mock_doc_processor, mock_rag_system, mock_agent, agent_config, mock_env_api_key):
        """Testa inicialização com configuração"""
        agent = HybridRAGAgent(agent_config)
        
        assert agent.config == agent_config
        assert mock_rag_system.called
        assert mock_doc_processor.called
        assert mock_agent.called
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_initialization_without_config(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key):
        """Testa inicialização sem configuração"""
        agent = HybridRAGAgent()
        
        assert agent.config is not None
        assert agent.config.agent_name == "Agente RAG Híbrido"
    
    def test_initialization_without_api_key(self, monkeypatch):
        """Testa inicialização sem API key"""
        monkeypatch.delenv("GOOGLE_API_KEY", raising=False)
        
        with pytest.raises(ValueError, match="API key do Google não encontrada"):
            HybridRAGAgent()
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_index_text(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key):
        """Testa indexação de texto"""
        # Configurar mocks
        mock_doc_instance = Mock()
        mock_doc_instance.process_text.return_value = [Mock(), Mock()]  # 2 documentos
        mock_doc_processor.return_value = mock_doc_instance
        
        mock_rag_instance = Mock()
        mock_rag_system.return_value = mock_rag_instance
        
        agent = HybridRAGAgent()
        
        result = agent.index_text("Texto de teste", "fonte_teste")
        
        assert result == 2
        mock_doc_instance.process_text.assert_called_once()
        mock_rag_instance.add_documents.assert_called_once()
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_index_documents_file(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key, tmp_path):
        """Testa indexação de arquivo"""
        # Criar arquivo temporário
        test_file = tmp_path / "test.txt"
        test_file.write_text("Conteúdo de teste")
        
        # Configurar mocks
        mock_doc_instance = Mock()
        mock_doc_instance.process_file.return_value = [Mock()]  # 1 documento
        mock_doc_processor.return_value = mock_doc_instance
        
        mock_rag_instance = Mock()
        mock_rag_system.return_value = mock_rag_instance
        
        agent = HybridRAGAgent()
        
        result = agent.index_documents(str(test_file))
        
        assert result == 1
        mock_doc_instance.process_file.assert_called_once()
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_index_documents_directory(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key, tmp_path):
        """Testa indexação de diretório"""
        # Criar diretório com arquivos
        test_dir = tmp_path / "docs"
        test_dir.mkdir()
        (test_dir / "doc1.txt").write_text("Documento 1")
        (test_dir / "doc2.txt").write_text("Documento 2")
        
        # Configurar mocks
        mock_doc_instance = Mock()
        mock_doc_instance.process_directory.return_value = [Mock(), Mock()]  # 2 documentos
        mock_doc_processor.return_value = mock_doc_instance
        
        mock_rag_instance = Mock()
        mock_rag_system.return_value = mock_rag_instance
        
        agent = HybridRAGAgent()
        
        result = agent.index_documents(str(test_dir))
        
        assert result == 2
        mock_doc_instance.process_directory.assert_called_once()
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_query(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key):
        """Testa consulta ao agente"""
        # Configurar mocks
        mock_rag_instance = Mock()
        mock_rag_response = Mock()
        mock_rag_response.context_used = "Contexto de teste"
        mock_rag_response.retrieved_documents = [Mock()]
        mock_rag_instance.query.return_value = mock_rag_response
        mock_rag_system.return_value = mock_rag_instance
        
        mock_agent_instance = Mock()
        mock_agent_response = Mock()
        mock_agent_response.content = "Resposta do agente"
        mock_agent_instance.run.return_value = mock_agent_response
        mock_agent.return_value = mock_agent_instance
        
        agent = HybridRAGAgent()
        
        result = agent.query("Pergunta de teste")
        
        assert result == "Resposta do agente"
        mock_rag_instance.query.assert_called_once()
        mock_agent_instance.run.assert_called_once()
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    async def test_query_async(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key):
        """Testa consulta assíncrona"""
        # Configurar mocks
        mock_rag_instance = Mock()
        mock_rag_response = Mock()
        mock_rag_response.context_used = "Contexto assíncrono"
        mock_rag_response.retrieved_documents = []
        
        # Mock para método assíncrono
        async def mock_query_async(*args, **kwargs):
            return mock_rag_response
        
        mock_rag_instance.query_async = mock_query_async
        mock_rag_system.return_value = mock_rag_instance
        
        mock_agent_instance = Mock()
        mock_agent_response = Mock()
        mock_agent_response.content = "Resposta assíncrona"
        mock_agent_instance.run.return_value = mock_agent_response
        mock_agent.return_value = mock_agent_instance
        
        agent = HybridRAGAgent()
        
        result = await agent.query_async("Pergunta assíncrona")
        
        assert result == "Resposta assíncrona"
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_get_stats(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key):
        """Testa obtenção de estatísticas"""
        # Configurar mocks
        mock_rag_instance = Mock()
        mock_rag_instance.get_stats.return_value = {"rag": "stats"}
        mock_rag_system.return_value = mock_rag_instance
        
        mock_doc_instance = Mock()
        mock_doc_instance.config.chunk_size = 1000
        mock_doc_instance.config.chunk_overlap = 200
        mock_doc_instance.config.supported_extensions = [".txt", ".pdf"]
        mock_doc_processor.return_value = mock_doc_instance
        
        agent = HybridRAGAgent()
        
        stats = agent.get_stats()
        
        assert "agent_config" in stats
        assert "rag_system" in stats
        assert "processing_config" in stats
        assert stats["rag_system"] == {"rag": "stats"}
    
    @patch('src.agent.Agent')
    @patch('src.agent.RAGSystem')
    @patch('src.agent.DocumentProcessor')
    def test_test_system(self, mock_doc_processor, mock_rag_system, mock_agent, mock_env_api_key):
        """Testa função de teste do sistema"""
        # Configurar mocks
        mock_rag_instance = Mock()
        mock_rag_instance.test_system.return_value = {"rag_test": True}
        mock_rag_system.return_value = mock_rag_instance
        
        mock_agent_instance = Mock()
        mock_agent_response = Mock()
        mock_agent_response.content = "OK"
        mock_agent_instance.run.return_value = mock_agent_response
        mock_agent.return_value = mock_agent_instance
        
        agent = HybridRAGAgent()
        
        test_results = agent.test_system()
        
        assert "rag_test" in test_results
        assert "agno_agent" in test_results
        assert "full_integration" in test_results
        assert test_results["rag_test"] is True
        assert test_results["agno_agent"] is True
    
    def test_build_agno_prompt(self, mock_env_api_key):
        """Testa construção de prompt para AGNO"""
        with patch('src.agent.Agent'), \
             patch('src.agent.RAGSystem'), \
             patch('src.agent.DocumentProcessor'):
            
            agent = HybridRAGAgent()
            
            # Mock da resposta RAG
            mock_rag_response = Mock()
            mock_rag_response.context_used = "Contexto de teste"
            mock_rag_response.retrieved_documents = [
                Mock(score=0.9, document=Mock(content="Documento 1 conteúdo...")),
                Mock(score=0.7, document=Mock(content="Documento 2 conteúdo..."))
            ]
            
            prompt = agent._build_agno_prompt("Pergunta teste", mock_rag_response)
            
            assert "PERGUNTA DO USUÁRIO: Pergunta teste" in prompt
            assert "CONTEXTO RECUPERADO:" in prompt
            assert "Contexto de teste" in prompt
            assert "DOCUMENTOS ENCONTRADOS: 2" in prompt
            assert "Score: 0.900" in prompt


class TestAgentConfig:
    """Testes para AgentConfig"""
    
    def test_agent_config_defaults(self):
        """Testa valores padrão da configuração"""
        config = AgentConfig()
        
        assert config.agent_name == "Agente RAG Híbrido"
        assert config.agent_role == "Especialista em recuperação e análise de informações"
        assert config.temperature == 0.7
        assert config.use_reasoning is True
        assert config.markdown_output is True
        assert config.model_name == "gemini-2.0-flash-exp"
    
    def test_agent_config_custom(self):
        """Testa configuração personalizada"""
        config = AgentConfig(
            agent_name="Agente Personalizado",
            temperature=0.5,
            use_reasoning=False
        )
        
        assert config.agent_name == "Agente Personalizado"
        assert config.temperature == 0.5
        assert config.use_reasoning is False

"""
Configurações compartilhadas para testes
"""

import pytest
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock


@pytest.fixture(scope="session")
def temp_data_dir():
    """Cria diretório temporário para dados de teste"""
    temp_dir = tempfile.mkdtemp(prefix="rag_test_")
    yield Path(temp_dir)
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_text_files(temp_data_dir):
    """Cria arquivos de texto de exemplo"""
    files = []
    
    # Arquivo 1: Sobre IA
    file1 = temp_data_dir / "ia_basics.txt"
    file1.write_text("""
    Inteligência Artificial (IA) é uma área da ciência da computação que se concentra 
    no desenvolvimento de sistemas capazes de realizar tarefas que normalmente requerem 
    inteligência humana. Isso inclui aprendizado, rac<PERSON><PERSON><PERSON><PERSON>, percepção, compreensão 
    de linguagem natural e resolução de problemas.
    """, encoding="utf-8")
    files.append(file1)
    
    # Arquivo 2: Sobre ML
    file2 = temp_data_dir / "machine_learning.txt"
    file2.write_text("""
    Machine Learning é um subcampo da IA que permite que os computadores aprendam e 
    melhorem automaticamente através da experiência, sem serem explicitamente programados. 
    Os algoritmos de machine learning constroem modelos matemáticos baseados em dados 
    de treinamento para fazer previsões ou decisões.
    """, encoding="utf-8")
    files.append(file2)
    
    # Arquivo 3: Sobre Deep Learning
    file3 = temp_data_dir / "deep_learning.txt"
    file3.write_text("""
    Deep Learning é uma técnica de machine learning baseada em redes neurais artificiais 
    com múltiplas camadas. É especialmente eficaz em tarefas como reconhecimento de 
    imagem, processamento de linguagem natural e reconhecimento de fala.
    """, encoding="utf-8")
    files.append(file3)
    
    return files


@pytest.fixture
def mock_api_key(monkeypatch):
    """Mock da API key do Google"""
    monkeypatch.setenv("GOOGLE_API_KEY", "test_api_key_12345")


@pytest.fixture
def mock_gemini_response():
    """Mock de resposta do Gemini"""
    return "Esta é uma resposta de teste do modelo Gemini."


@pytest.fixture
def sample_documents():
    """Documentos de exemplo para testes"""
    from src.hybrid_search import Document
    
    return [
        Document(
            id="doc1",
            content="Python é uma linguagem de programação interpretada de alto nível",
            metadata={"tipo": "definição", "linguagem": "python"}
        ),
        Document(
            id="doc2",
            content="JavaScript é usado para desenvolvimento web front-end e back-end",
            metadata={"tipo": "definição", "linguagem": "javascript"}
        ),
        Document(
            id="doc3",
            content="Algoritmos de machine learning podem ser supervisionados ou não supervisionados",
            metadata={"tipo": "conceito", "área": "ml"}
        ),
        Document(
            id="doc4",
            content="Redes neurais convolucionais são eficazes para processamento de imagens",
            metadata={"tipo": "técnica", "área": "deep_learning"}
        )
    ]


@pytest.fixture
def mock_embedding_model():
    """Mock do modelo de embedding"""
    mock_model = Mock()
    mock_model.encode.return_value = [[0.1, 0.2, 0.3, 0.4]] * 4  # 4 embeddings de exemplo
    return mock_model


@pytest.fixture
def skip_if_no_api_key():
    """Skip teste se não houver API key real"""
    if not os.getenv("GOOGLE_API_KEY") or os.getenv("GOOGLE_API_KEY") == "test_api_key_12345":
        pytest.skip("API key real do Google não configurada")


@pytest.fixture
def integration_test_marker():
    """Marcador para testes de integração"""
    return pytest.mark.integration


# Configurações de pytest
def pytest_configure(config):
    """Configuração do pytest"""
    config.addinivalue_line(
        "markers", "integration: marca testes de integração que requerem API key real"
    )
    config.addinivalue_line(
        "markers", "slow: marca testes que demoram para executar"
    )


def pytest_collection_modifyitems(config, items):
    """Modifica itens de teste coletados"""
    # Adicionar marcador slow para testes que demoram
    for item in items:
        if "test_full_integration" in item.name or "test_end_to_end" in item.name:
            item.add_marker(pytest.mark.slow)


# Fixtures para mocking de componentes externos
@pytest.fixture
def mock_faiss_index():
    """Mock do índice FAISS"""
    mock_index = Mock()
    mock_index.ntotal = 0
    mock_index.search.return_value = (
        [[0.8, 0.6, 0.4]],  # distances
        [[0, 1, 2]]         # indices
    )
    return mock_index


@pytest.fixture
def mock_bm25_index():
    """Mock do índice BM25"""
    mock_bm25 = Mock()
    mock_bm25.get_scores.return_value = [0.9, 0.7, 0.5, 0.3]
    return mock_bm25


@pytest.fixture
def mock_sentence_transformer():
    """Mock do SentenceTransformer"""
    with pytest.mock.patch('sentence_transformers.SentenceTransformer') as mock:
        mock_instance = Mock()
        mock_instance.encode.return_value = [[0.1, 0.2, 0.3, 0.4]] * 10
        mock.return_value = mock_instance
        yield mock_instance

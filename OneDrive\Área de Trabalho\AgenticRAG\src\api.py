"""
API FastAPI para o Agente RAG Híbrido
"""

import os
import tempfile
from typing import List, Dict, Any, Optional
from pathlib import Path
import asyncio
import shutil

from fastapi import FastAPI, HTTPException, UploadFile, File, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
from loguru import logger

from .agent import HybridRAGAgent, AgentConfig
from .rag_system import RAGConfig
from .document_processor import ProcessingConfig
from .gemini_client import GeminiConfig


# Modelos Pydantic para API
class QueryRequest(BaseModel):
    """Modelo para requisição de consulta"""
    question: str = Field(..., description="Pergunta para o agente")
    max_retrieved_docs: Optional[int] = Field(5, description="Máximo de documentos a recuperar")
    search_type: Optional[str] = Field("hybrid", description="Tipo de busca: semantic, lexical, hybrid")
    min_relevance_score: Optional[float] = Field(0.1, description="Score mínimo de relevância")


class QueryResponse(BaseModel):
    """Modelo para resposta de consulta"""
    answer: str = Field(..., description="Resposta do agente")
    retrieved_documents: int = Field(..., description="Número de documentos recuperados")
    search_type: str = Field(..., description="Tipo de busca utilizado")
    processing_time: float = Field(..., description="Tempo de processamento em segundos")


class IndexTextRequest(BaseModel):
    """Modelo para indexação de texto"""
    text: str = Field(..., description="Texto para indexar")
    source_name: str = Field("text_input", description="Nome da fonte")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Metadados adicionais")


class IndexResponse(BaseModel):
    """Modelo para resposta de indexação"""
    documents_indexed: int = Field(..., description="Número de documentos indexados")
    message: str = Field(..., description="Mensagem de status")


class StatsResponse(BaseModel):
    """Modelo para resposta de estatísticas"""
    agent_stats: Dict[str, Any] = Field(..., description="Estatísticas do agente")


class HealthResponse(BaseModel):
    """Modelo para resposta de saúde"""
    status: str = Field(..., description="Status da aplicação")
    tests: Dict[str, bool] = Field(..., description="Resultados dos testes")


# Configuração da aplicação
class APIConfig:
    """Configuração da API"""
    def __init__(self):
        self.title = os.getenv("APP_NAME", "Agente RAG Híbrido API")
        self.version = os.getenv("APP_VERSION", "1.0.0")
        self.debug = os.getenv("DEBUG", "false").lower() == "true"
        self.host = os.getenv("HOST", "0.0.0.0")
        self.port = int(os.getenv("PORT", "8000"))


# Inicializar aplicação
api_config = APIConfig()
app = FastAPI(
    title=api_config.title,
    version=api_config.version,
    description="API para agente inteligente com RAG e busca híbrida usando AGNO e Gemini 2.0 Flash"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Instância global do agente
agent: Optional[HybridRAGAgent] = None


@app.on_event("startup")
async def startup_event():
    """Inicializa o agente na inicialização da aplicação"""
    global agent
    
    try:
        logger.info("Inicializando agente RAG híbrido...")
        
        # Configurar agente
        gemini_config = GeminiConfig(
            model_name="gemini-2.0-flash-exp",
            temperature=0.7
        )
        
        rag_config = RAGConfig(
            max_retrieved_docs=int(os.getenv("MAX_SEARCH_RESULTS", "10")),
            gemini_config=gemini_config
        )
        
        processing_config = ProcessingConfig(
            chunk_size=int(os.getenv("CHUNK_SIZE", "1000")),
            chunk_overlap=int(os.getenv("CHUNK_OVERLAP", "200"))
        )
        
        agent_config = AgentConfig(
            rag_config=rag_config,
            processing_config=processing_config,
            agent_name="Agente RAG Híbrido API",
            use_reasoning=True
        )
        
        agent = HybridRAGAgent(agent_config)
        
        logger.info("Agente inicializado com sucesso")
        
    except Exception as e:
        logger.error(f"Erro ao inicializar agente: {e}")
        raise


@app.get("/", response_model=Dict[str, str])
async def root():
    """Endpoint raiz"""
    return {
        "message": "Agente RAG Híbrido API",
        "version": api_config.version,
        "status": "running"
    }


@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Verifica a saúde da aplicação"""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agente não inicializado")
    
    try:
        tests = agent.test_system()
        status = "healthy" if all(tests.values()) else "degraded"
        
        return HealthResponse(status=status, tests=tests)
        
    except Exception as e:
        logger.error(f"Erro no health check: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/stats", response_model=StatsResponse)
async def get_stats():
    """Retorna estatísticas do agente"""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agente não inicializado")
    
    try:
        stats = agent.get_stats()
        return StatsResponse(agent_stats=stats)
        
    except Exception as e:
        logger.error(f"Erro ao obter estatísticas: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/query", response_model=QueryResponse)
async def query_agent(request: QueryRequest):
    """Faz uma consulta ao agente"""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agente não inicializado")
    
    try:
        import time
        start_time = time.time()
        
        # Fazer consulta
        answer = await agent.query_async(
            question=request.question,
            max_retrieved_docs=request.max_retrieved_docs,
            search_type=request.search_type,
            min_relevance_score=request.min_relevance_score
        )
        
        processing_time = time.time() - start_time
        
        # Obter informações sobre documentos recuperados
        rag_response = agent.rag_system.query(
            request.question,
            max_retrieved_docs=request.max_retrieved_docs,
            search_type=request.search_type,
            min_relevance_score=request.min_relevance_score
        )
        
        return QueryResponse(
            answer=answer,
            retrieved_documents=len(rag_response.retrieved_documents),
            search_type=request.search_type,
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error(f"Erro na consulta: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/index/text", response_model=IndexResponse)
async def index_text(request: IndexTextRequest):
    """Indexa texto diretamente"""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agente não inicializado")
    
    try:
        documents_count = agent.index_text(
            text=request.text,
            source_name=request.source_name,
            metadata=request.metadata
        )
        
        return IndexResponse(
            documents_indexed=documents_count,
            message=f"Texto indexado com sucesso em {documents_count} chunks"
        )
        
    except Exception as e:
        logger.error(f"Erro ao indexar texto: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/index/files", response_model=IndexResponse)
async def index_files(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...)
):
    """Indexa arquivos enviados"""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agente não inicializado")
    
    if not files:
        raise HTTPException(status_code=400, detail="Nenhum arquivo enviado")
    
    try:
        # Criar diretório temporário
        temp_dir = tempfile.mkdtemp()
        temp_files = []
        
        # Salvar arquivos temporariamente
        for file in files:
            if file.filename:
                temp_file_path = Path(temp_dir) / file.filename
                temp_files.append(temp_file_path)
                
                with open(temp_file_path, "wb") as buffer:
                    shutil.copyfileobj(file.file, buffer)
        
        # Indexar arquivos
        documents_count = agent.index_documents(temp_files)
        
        # Agendar limpeza dos arquivos temporários
        background_tasks.add_task(cleanup_temp_dir, temp_dir)
        
        return IndexResponse(
            documents_indexed=documents_count,
            message=f"{len(files)} arquivo(s) processado(s), {documents_count} documentos indexados"
        )
        
    except Exception as e:
        logger.error(f"Erro ao indexar arquivos: {e}")
        # Limpar arquivos temporários em caso de erro
        if 'temp_dir' in locals():
            cleanup_temp_dir(temp_dir)
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/index/directory")
async def index_directory(
    directory_path: str,
    recursive: bool = True
):
    """Indexa um diretório local (apenas para desenvolvimento)"""
    if agent is None:
        raise HTTPException(status_code=503, detail="Agente não inicializado")
    
    # Verificar se o diretório existe
    dir_path = Path(directory_path)
    if not dir_path.exists() or not dir_path.is_dir():
        raise HTTPException(status_code=400, detail="Diretório não encontrado")
    
    try:
        documents_count = agent.index_documents(dir_path, recursive=recursive)
        
        return IndexResponse(
            documents_indexed=documents_count,
            message=f"Diretório indexado: {documents_count} documentos processados"
        )
        
    except Exception as e:
        logger.error(f"Erro ao indexar diretório: {e}")
        raise HTTPException(status_code=500, detail=str(e))


def cleanup_temp_dir(temp_dir: str):
    """Limpa diretório temporário"""
    try:
        shutil.rmtree(temp_dir)
        logger.info(f"Diretório temporário removido: {temp_dir}")
    except Exception as e:
        logger.error(f"Erro ao remover diretório temporário {temp_dir}: {e}")


# Função para executar o servidor
def run_server():
    """Executa o servidor da API"""
    uvicorn.run(
        "src.api:app",
        host=api_config.host,
        port=api_config.port,
        reload=api_config.debug,
        log_level="info"
    )


if __name__ == "__main__":
    run_server()

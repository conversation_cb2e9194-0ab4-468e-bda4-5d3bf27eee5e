"""
Exemplo básico de uso do Agente RAG Híbrido
"""

import os
from pathlib import Path
from src.agent import HybridRAGAgent


def main():
    """Exemplo básico de uso"""
    
    # Verificar se a API key está configurada
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY não configurada!")
        print("Configure a variável de ambiente ou arquivo .env")
        return
    
    print("🚀 Inicializando Agente RAG Híbrido...")
    
    try:
        # Inicializar agente
        agent = HybridRAGAgent()
        print("✅ Agente inicializado com sucesso!")
        
        # Testar sistema
        print("\n🔍 Testando componentes do sistema...")
        tests = agent.test_system()
        
        for component, status in tests.items():
            status_icon = "✅" if status else "❌"
            print(f"  {status_icon} {component}")
        
        if not all(tests.values()):
            print("❌ Alguns componentes falharam nos testes!")
            return
        
        # Indexar texto de exemplo
        print("\n📚 Indexando texto de exemplo...")
        
        exemplo_texto = """
        Inteligência Artificial (IA) é uma área da ciência da computação que se concentra 
        no desenvolvimento de sistemas capazes de realizar tarefas que normalmente requerem 
        inteligência humana. Isso inclui aprendizado, raciocínio, percepção, compreensão 
        de linguagem natural e resolução de problemas.
        
        Machine Learning é um subcampo da IA que permite que os computadores aprendam e 
        melhorem automaticamente através da experiência, sem serem explicitamente programados. 
        Os algoritmos de machine learning constroem modelos matemáticos baseados em dados 
        de treinamento para fazer previsões ou decisões.
        
        Deep Learning é uma técnica de machine learning baseada em redes neurais artificiais 
        com múltiplas camadas. É especialmente eficaz em tarefas como reconhecimento de 
        imagem, processamento de linguagem natural e reconhecimento de fala.
        
        Natural Language Processing (NLP) é uma área da IA que se concentra na interação 
        entre computadores e linguagem humana. O objetivo é permitir que os computadores 
        compreendam, interpretem e gerem linguagem humana de forma útil.
        """
        
        chunks_indexados = agent.index_text(
            text=exemplo_texto,
            source_name="conceitos_ia",
            metadata={"categoria": "educacional", "tema": "inteligência artificial"}
        )
        
        print(f"✅ Texto indexado em {chunks_indexados} chunks")
        
        # Fazer consultas de exemplo
        print("\n💬 Fazendo consultas de exemplo...")
        
        perguntas = [
            "O que é Inteligência Artificial?",
            "Qual a diferença entre Machine Learning e Deep Learning?",
            "Como funciona o processamento de linguagem natural?",
            "Quais são as aplicações de deep learning?"
        ]
        
        for i, pergunta in enumerate(perguntas, 1):
            print(f"\n📝 Pergunta {i}: {pergunta}")
            print("🤔 Processando...")
            
            resposta = agent.query(pergunta)
            print(f"🤖 Resposta: {resposta}")
            print("-" * 80)
        
        # Mostrar estatísticas
        print("\n📊 Estatísticas do sistema:")
        stats = agent.get_stats()
        
        print(f"  📄 Documentos indexados: {stats['rag_system']['search_engine']['total_documents']}")
        print(f"  🧠 Modelo usado: {stats['rag_system']['llm_model']['name']}")
        print(f"  🔍 Tipo de busca padrão: {stats['rag_system']['config']['search_type']}")
        print(f"  📏 Tamanho do chunk: {stats['processing_config']['chunk_size']}")
        
        print("\n✅ Exemplo concluído com sucesso!")
        
    except Exception as e:
        print(f"❌ Erro durante execução: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

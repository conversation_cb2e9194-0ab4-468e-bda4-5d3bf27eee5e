"""
Testes para o sistema RAG
"""

import pytest
import os
from unittest.mock import Mock, patch
from src.rag_system import RAGSystem, RAGConfig, RAGResponse
from src.gemini_client import GeminiConfig
from src.hybrid_search import Document


class TestRAGSystem:
    """Testes para RAGSystem"""
    
    @pytest.fixture
    def mock_gemini_config(self):
        """Fixture para configuração mock do Gemini"""
        return GeminiConfig(
            model_name="gemini-2.0-flash-exp",
            api_key="test_key",
            temperature=0.7
        )
    
    @pytest.fixture
    def rag_config(self, mock_gemini_config):
        """Fixture para configuração RAG"""
        return RAGConfig(
            max_retrieved_docs=5,
            search_type="hybrid",
            gemini_config=mock_gemini_config
        )
    
    @pytest.fixture
    def sample_documents(self):
        """Fixture com documentos de exemplo"""
        return [
            Document(
                id="doc1",
                content="Python é uma linguagem de programação de alto nível",
                metadata={"tipo": "definição"}
            ),
            Document(
                id="doc2",
                content="Machine learning é usado para criar modelos preditivos",
                metadata={"tipo": "conceito"}
            ),
            Document(
                id="doc3",
                content="Redes neurais são inspiradas no cérebro humano",
                metadata={"tipo": "analogia"}
            )
        ]
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_initialization(self, mock_gemini_client, rag_config):
        """Testa inicialização do sistema RAG"""
        rag_system = RAGSystem(rag_config)
        
        assert rag_system.config == rag_config
        assert rag_system.search_engine is not None
        assert mock_gemini_client.called
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_add_documents(self, mock_gemini_client, rag_config, sample_documents):
        """Testa adição de documentos"""
        rag_system = RAGSystem(rag_config)
        
        rag_system.add_documents(sample_documents)
        
        assert len(rag_system.search_engine.documents) == 3
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_query_without_documents(self, mock_gemini_client, rag_config):
        """Testa consulta sem documentos indexados"""
        mock_client_instance = Mock()
        mock_client_instance.generate_rag_response.return_value = "Não há documentos indexados."
        mock_gemini_client.return_value = mock_client_instance
        
        rag_system = RAGSystem(rag_config)
        
        response = rag_system.query("O que é Python?")
        
        assert isinstance(response, RAGResponse)
        assert response.answer == "Não há documentos indexados."
        assert len(response.retrieved_documents) == 0
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_query_with_documents(self, mock_gemini_client, rag_config, sample_documents):
        """Testa consulta com documentos indexados"""
        mock_client_instance = Mock()
        mock_client_instance.generate_rag_response.return_value = "Python é uma linguagem de programação."
        mock_gemini_client.return_value = mock_client_instance
        
        rag_system = RAGSystem(rag_config)
        rag_system.add_documents(sample_documents)
        
        response = rag_system.query("O que é Python?")
        
        assert isinstance(response, RAGResponse)
        assert response.answer == "Python é uma linguagem de programação."
        assert len(response.retrieved_documents) > 0
        assert response.context_used is not None
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_build_context(self, mock_gemini_client, rag_config, sample_documents):
        """Testa construção de contexto"""
        rag_system = RAGSystem(rag_config)
        rag_system.add_documents(sample_documents)
        
        # Simular resultados de busca
        search_results = rag_system.search_engine.search("Python", top_k=2)
        
        context = rag_system._build_context(search_results)
        
        assert isinstance(context, str)
        assert len(context) > 0
        assert "Python" in context or "python" in context.lower()
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_get_stats(self, mock_gemini_client, rag_config, sample_documents):
        """Testa obtenção de estatísticas"""
        rag_system = RAGSystem(rag_config)
        rag_system.add_documents(sample_documents)
        
        stats = rag_system.get_stats()
        
        assert "config" in stats
        assert "search_engine" in stats
        assert "llm_model" in stats
        assert stats["search_engine"]["total_documents"] == 3
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_test_system(self, mock_gemini_client, rag_config):
        """Testa função de teste do sistema"""
        mock_client_instance = Mock()
        mock_client_instance.test_connection.return_value = True
        mock_gemini_client.return_value = mock_client_instance
        
        rag_system = RAGSystem(rag_config)
        
        test_results = rag_system.test_system()
        
        assert isinstance(test_results, dict)
        assert "search_engine" in test_results
        assert "gemini_client" in test_results
    
    @patch('src.rag_system.GeminiRAGClient')
    def test_query_with_custom_params(self, mock_gemini_client, rag_config, sample_documents):
        """Testa consulta com parâmetros personalizados"""
        mock_client_instance = Mock()
        mock_client_instance.generate_rag_response.return_value = "Resposta personalizada"
        mock_gemini_client.return_value = mock_client_instance
        
        rag_system = RAGSystem(rag_config)
        rag_system.add_documents(sample_documents)
        
        response = rag_system.query(
            "Teste",
            max_retrieved_docs=2,
            search_type="semantic",
            min_relevance_score=0.5
        )
        
        assert isinstance(response, RAGResponse)
        assert response.answer == "Resposta personalizada"
    
    @patch('src.rag_system.GeminiRAGClient')
    @patch('src.rag_system.asyncio')
    async def test_query_async(self, mock_asyncio, mock_gemini_client, rag_config, sample_documents):
        """Testa consulta assíncrona"""
        mock_client_instance = Mock()
        mock_client_instance.generate_rag_response.return_value = "Resposta assíncrona"
        mock_gemini_client.return_value = mock_client_instance
        
        rag_system = RAGSystem(rag_config)
        rag_system.add_documents(sample_documents)
        
        response = await rag_system.query_async("Teste assíncrono")
        
        assert isinstance(response, RAGResponse)
        assert response.answer == "Resposta assíncrona"


class TestRAGConfig:
    """Testes para RAGConfig"""
    
    def test_rag_config_creation(self):
        """Testa criação de configuração RAG"""
        gemini_config = GeminiConfig(api_key="test")
        
        config = RAGConfig(
            max_retrieved_docs=10,
            search_type="semantic",
            gemini_config=gemini_config
        )
        
        assert config.max_retrieved_docs == 10
        assert config.search_type == "semantic"
        assert config.gemini_config == gemini_config
    
    def test_rag_config_defaults(self):
        """Testa valores padrão da configuração"""
        config = RAGConfig()
        
        assert config.max_retrieved_docs == 5
        assert config.search_type == "hybrid"
        assert config.min_relevance_score == 0.1
        assert config.max_context_length == 6000


class TestRAGResponse:
    """Testes para RAGResponse"""
    
    def test_rag_response_creation(self):
        """Testa criação de resposta RAG"""
        doc = Document(id="test", content="teste")
        from src.hybrid_search import SearchResult
        
        search_result = SearchResult(
            document=doc,
            score=0.8,
            search_type="hybrid"
        )
        
        response = RAGResponse(
            answer="Resposta de teste",
            retrieved_documents=[search_result],
            context_used="Contexto de teste",
            search_type="hybrid"
        )
        
        assert response.answer == "Resposta de teste"
        assert len(response.retrieved_documents) == 1
        assert response.context_used == "Contexto de teste"
        assert response.search_type == "hybrid"

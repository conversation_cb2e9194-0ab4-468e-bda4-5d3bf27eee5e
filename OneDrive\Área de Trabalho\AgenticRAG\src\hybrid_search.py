"""
Sistema de Busca Híbrida combinando busca semântica e lexical
"""

import numpy as np
from typing import List, Dict, Tuple, Any, Optional
from dataclasses import dataclass
from sentence_transformers import SentenceTransformer
from rank_bm25 import BM25Okapi
import faiss
from loguru import logger


@dataclass
class Document:
    """Representa um documento no sistema"""
    id: str
    content: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


@dataclass
class SearchResult:
    """Resultado de uma busca"""
    document: Document
    score: float
    search_type: str  # 'semantic', 'lexical', 'hybrid'


class HybridSearchEngine:
    """
    Motor de busca híbrida que combina busca semântica (embeddings) 
    com busca lexical (BM25)
    """
    
    def __init__(
        self,
        embedding_model: str = "all-MiniLM-L6-v2",
        bm25_k1: float = 1.2,
        bm25_b: float = 0.75,
        semantic_weight: float = 0.6,
        lexical_weight: float = 0.4
    ):
        """
        Inicializa o motor de busca híbrida
        
        Args:
            embedding_model: Nome do modelo de embeddings
            bm25_k1: Parâmetro k1 do BM25
            bm25_b: Parâmetro b do BM25
            semantic_weight: Peso da busca semântica (0-1)
            lexical_weight: Peso da busca lexical (0-1)
        """
        self.embedding_model_name = embedding_model
        self.bm25_k1 = bm25_k1
        self.bm25_b = bm25_b
        self.semantic_weight = semantic_weight
        self.lexical_weight = lexical_weight
        
        # Inicializar componentes
        self.embedding_model = None
        self.faiss_index = None
        self.bm25_index = None
        self.documents: List[Document] = []
        self.document_embeddings = None
        
        logger.info(f"Inicializando HybridSearchEngine com modelo: {embedding_model}")
        self._initialize_models()
    
    def _initialize_models(self):
        """Inicializa os modelos de embedding"""
        try:
            self.embedding_model = SentenceTransformer(self.embedding_model_name)
            logger.info("Modelo de embeddings carregado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao carregar modelo de embeddings: {e}")
            raise
    
    def add_documents(self, documents: List[Document]):
        """
        Adiciona documentos ao índice
        
        Args:
            documents: Lista de documentos para indexar
        """
        logger.info(f"Adicionando {len(documents)} documentos ao índice")
        
        self.documents.extend(documents)
        
        # Extrair textos para indexação
        texts = [doc.content for doc in documents]
        
        # Criar embeddings semânticos
        self._update_semantic_index(texts)
        
        # Criar índice BM25
        self._update_bm25_index()
        
        logger.info("Documentos indexados com sucesso")
    
    def _update_semantic_index(self, new_texts: List[str]):
        """Atualiza o índice semântico FAISS"""
        try:
            # Gerar embeddings
            new_embeddings = self.embedding_model.encode(new_texts)
            
            if self.document_embeddings is None:
                self.document_embeddings = new_embeddings
            else:
                self.document_embeddings = np.vstack([self.document_embeddings, new_embeddings])
            
            # Criar/atualizar índice FAISS
            dimension = self.document_embeddings.shape[1]
            if self.faiss_index is None:
                self.faiss_index = faiss.IndexFlatIP(dimension)  # Inner Product para similaridade
            
            # Normalizar embeddings para usar cosine similarity
            faiss.normalize_L2(self.document_embeddings)
            
            # Reconstruir índice
            self.faiss_index.reset()
            self.faiss_index.add(self.document_embeddings.astype('float32'))
            
            logger.info(f"Índice semântico atualizado com {len(self.documents)} documentos")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar índice semântico: {e}")
            raise
    
    def _update_bm25_index(self):
        """Atualiza o índice BM25"""
        try:
            # Tokenizar documentos (simples split por espaços)
            tokenized_docs = [doc.content.lower().split() for doc in self.documents]
            
            # Criar índice BM25
            self.bm25_index = BM25Okapi(
                tokenized_docs,
                k1=self.bm25_k1,
                b=self.bm25_b
            )
            
            logger.info(f"Índice BM25 atualizado com {len(self.documents)} documentos")
            
        except Exception as e:
            logger.error(f"Erro ao atualizar índice BM25: {e}")
            raise
    
    def semantic_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        Busca semântica usando embeddings
        
        Args:
            query: Consulta de busca
            top_k: Número de resultados a retornar
            
        Returns:
            Lista de resultados ordenados por relevância
        """
        if self.faiss_index is None or len(self.documents) == 0:
            return []
        
        try:
            # Gerar embedding da consulta
            query_embedding = self.embedding_model.encode([query])
            faiss.normalize_L2(query_embedding)
            
            # Buscar no índice FAISS
            scores, indices = self.faiss_index.search(
                query_embedding.astype('float32'), 
                min(top_k, len(self.documents))
            )
            
            # Criar resultados
            results = []
            for score, idx in zip(scores[0], indices[0]):
                if idx < len(self.documents):  # Verificar índice válido
                    results.append(SearchResult(
                        document=self.documents[idx],
                        score=float(score),
                        search_type="semantic"
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"Erro na busca semântica: {e}")
            return []
    
    def lexical_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        Busca lexical usando BM25
        
        Args:
            query: Consulta de busca
            top_k: Número de resultados a retornar
            
        Returns:
            Lista de resultados ordenados por relevância
        """
        if self.bm25_index is None or len(self.documents) == 0:
            return []
        
        try:
            # Tokenizar consulta
            query_tokens = query.lower().split()
            
            # Calcular scores BM25
            scores = self.bm25_index.get_scores(query_tokens)
            
            # Ordenar por score e pegar top_k
            scored_docs = [(score, idx) for idx, score in enumerate(scores)]
            scored_docs.sort(key=lambda x: x[0], reverse=True)
            
            # Criar resultados
            results = []
            for score, idx in scored_docs[:top_k]:
                if score > 0:  # Apenas resultados com score positivo
                    results.append(SearchResult(
                        document=self.documents[idx],
                        score=float(score),
                        search_type="lexical"
                    ))
            
            return results
            
        except Exception as e:
            logger.error(f"Erro na busca lexical: {e}")
            return []
    
    def hybrid_search(self, query: str, top_k: int = 10) -> List[SearchResult]:
        """
        Busca híbrida combinando busca semântica e lexical
        
        Args:
            query: Consulta de busca
            top_k: Número de resultados a retornar
            
        Returns:
            Lista de resultados ordenados por relevância combinada
        """
        if len(self.documents) == 0:
            return []
        
        try:
            # Realizar buscas individuais
            semantic_results = self.semantic_search(query, top_k * 2)
            lexical_results = self.lexical_search(query, top_k * 2)
            
            # Combinar resultados
            combined_scores = {}
            
            # Adicionar scores semânticos
            for result in semantic_results:
                doc_id = result.document.id
                combined_scores[doc_id] = {
                    'document': result.document,
                    'semantic_score': result.score,
                    'lexical_score': 0.0
                }
            
            # Adicionar scores lexicais
            for result in lexical_results:
                doc_id = result.document.id
                if doc_id in combined_scores:
                    combined_scores[doc_id]['lexical_score'] = result.score
                else:
                    combined_scores[doc_id] = {
                        'document': result.document,
                        'semantic_score': 0.0,
                        'lexical_score': result.score
                    }
            
            # Calcular score híbrido
            hybrid_results = []
            for doc_id, scores in combined_scores.items():
                # Normalizar scores (opcional - pode ser melhorado)
                semantic_norm = scores['semantic_score']
                lexical_norm = scores['lexical_score'] / 10.0  # BM25 scores são maiores
                
                # Combinar com pesos
                hybrid_score = (
                    self.semantic_weight * semantic_norm + 
                    self.lexical_weight * lexical_norm
                )
                
                hybrid_results.append(SearchResult(
                    document=scores['document'],
                    score=hybrid_score,
                    search_type="hybrid"
                ))
            
            # Ordenar por score híbrido
            hybrid_results.sort(key=lambda x: x.score, reverse=True)
            
            return hybrid_results[:top_k]
            
        except Exception as e:
            logger.error(f"Erro na busca híbrida: {e}")
            return []
    
    def search(
        self, 
        query: str, 
        top_k: int = 10, 
        search_type: str = "hybrid"
    ) -> List[SearchResult]:
        """
        Interface principal de busca
        
        Args:
            query: Consulta de busca
            top_k: Número de resultados a retornar
            search_type: Tipo de busca ('semantic', 'lexical', 'hybrid')
            
        Returns:
            Lista de resultados ordenados por relevância
        """
        logger.info(f"Realizando busca {search_type}: '{query}' (top_k={top_k})")
        
        if search_type == "semantic":
            return self.semantic_search(query, top_k)
        elif search_type == "lexical":
            return self.lexical_search(query, top_k)
        elif search_type == "hybrid":
            return self.hybrid_search(query, top_k)
        else:
            raise ValueError(f"Tipo de busca inválido: {search_type}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas do índice"""
        return {
            "total_documents": len(self.documents),
            "embedding_model": self.embedding_model_name,
            "semantic_index_size": self.faiss_index.ntotal if self.faiss_index else 0,
            "bm25_configured": self.bm25_index is not None,
            "semantic_weight": self.semantic_weight,
            "lexical_weight": self.lexical_weight
        }

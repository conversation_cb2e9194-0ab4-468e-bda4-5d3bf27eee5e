# Agente RAG Híbrido com AGNO e Gemini 2.0 Flash

Um agente inteligente que combina Retrieval-Augmented Generation (RAG) com busca híbrida (semântica + lexical) usando o framework AGNO e o modelo Gemini 2.0 Flash.

## 🚀 Características

- **RAG Avançado**: Sistema de recuperação de informações que aumenta as capacidades do modelo
- **Busca Híbrida**: Combinação de busca semântica (embeddings) e lexical (BM25)
- **Gemini 2.0 Flash**: Modelo de linguagem de última geração do Google
- **Framework AGNO**: Arquitetura multi-agente com memória e raciocínio
- **Interface API**: FastAPI para interação programática
- **Suporte Multi-formato**: PDF, DOCX, TXT e mais

## 📋 Pré-requisitos

- Python 3.8+
- Chave da API do Google Gemini
- 4GB+ de RAM (recomendado)

## 🛠️ Instalação

1. Clone o repositório:
```bash
git clone <repository-url>
cd AgenticRAG
```

2. Crie um ambiente virtual:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate  # Windows
```

3. Instale as dependências:
```bash
pip install -r requirements.txt
```

4. Configure as variáveis de ambiente:
```bash
cp .env.example .env
# Edite o arquivo .env com suas configurações
```

## 🚀 Uso Rápido

```python
from src.agent import HybridRAGAgent

# Inicializar o agente
agent = HybridRAGAgent()

# Indexar documentos
agent.index_documents("path/to/documents/")

# Fazer uma consulta
response = agent.query("Qual é o tema principal dos documentos?")
print(response)
```

## 📚 Documentação

- [Arquitetura do Sistema](docs/architecture.md)
- [Guia de Uso](docs/usage.md)
- [API Reference](docs/api.md)
- [Exemplos](examples/)

## 🧪 Testes

```bash
pytest tests/
```

## 📄 Licença

MIT License

"""
Cliente para integração com Google Gemini 2.0 Flash
"""

import os
from typing import List, Dict, Any, Optional, AsyncGenerator
import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
from loguru import logger
from dataclasses import dataclass


@dataclass
class GeminiConfig:
    """Configuração para o cliente Gemini"""
    model_name: str = "gemini-2.0-flash-exp"
    temperature: float = 0.7
    top_p: float = 0.8
    top_k: int = 40
    max_output_tokens: int = 8192
    api_key: Optional[str] = None


class GeminiClient:
    """
    Cliente para interação com Google Gemini 2.0 Flash
    """
    
    def __init__(self, config: Optional[GeminiConfig] = None):
        """
        Inicializa o cliente Gemini
        
        Args:
            config: Configuração do cliente
        """
        self.config = config or GeminiConfig()
        
        # Configurar API key
        api_key = self.config.api_key or os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError(
                "API key do Google não encontrada. "
                "Configure GOOGLE_API_KEY ou passe via config.api_key"
            )
        
        genai.configure(api_key=api_key)
        
        # Configurar modelo
        self.model = genai.GenerativeModel(
            model_name=self.config.model_name,
            generation_config=genai.types.GenerationConfig(
                temperature=self.config.temperature,
                top_p=self.config.top_p,
                top_k=self.config.top_k,
                max_output_tokens=self.config.max_output_tokens,
            ),
            safety_settings={
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
        )
        
        logger.info(f"Cliente Gemini inicializado com modelo: {self.config.model_name}")
    
    def generate_response(
        self, 
        prompt: str, 
        context: Optional[str] = None,
        system_instruction: Optional[str] = None
    ) -> str:
        """
        Gera uma resposta usando o modelo Gemini
        
        Args:
            prompt: Prompt principal
            context: Contexto adicional (ex: documentos recuperados)
            system_instruction: Instrução do sistema
            
        Returns:
            Resposta gerada pelo modelo
        """
        try:
            # Construir prompt completo
            full_prompt = self._build_prompt(prompt, context, system_instruction)
            
            logger.info(f"Gerando resposta para prompt de {len(full_prompt)} caracteres")
            
            # Gerar resposta
            response = self.model.generate_content(full_prompt)
            
            if response.text:
                logger.info("Resposta gerada com sucesso")
                return response.text
            else:
                logger.warning("Resposta vazia recebida do modelo")
                return "Desculpe, não consegui gerar uma resposta adequada."
                
        except Exception as e:
            logger.error(f"Erro ao gerar resposta: {e}")
            return f"Erro ao processar sua solicitação: {str(e)}"
    
    async def generate_response_async(
        self, 
        prompt: str, 
        context: Optional[str] = None,
        system_instruction: Optional[str] = None
    ) -> str:
        """
        Versão assíncrona da geração de resposta
        
        Args:
            prompt: Prompt principal
            context: Contexto adicional
            system_instruction: Instrução do sistema
            
        Returns:
            Resposta gerada pelo modelo
        """
        try:
            # Construir prompt completo
            full_prompt = self._build_prompt(prompt, context, system_instruction)
            
            logger.info(f"Gerando resposta assíncrona para prompt de {len(full_prompt)} caracteres")
            
            # Gerar resposta assíncrona
            response = await self.model.generate_content_async(full_prompt)
            
            if response.text:
                logger.info("Resposta assíncrona gerada com sucesso")
                return response.text
            else:
                logger.warning("Resposta vazia recebida do modelo")
                return "Desculpe, não consegui gerar uma resposta adequada."
                
        except Exception as e:
            logger.error(f"Erro ao gerar resposta assíncrona: {e}")
            return f"Erro ao processar sua solicitação: {str(e)}"
    
    def generate_stream(
        self, 
        prompt: str, 
        context: Optional[str] = None,
        system_instruction: Optional[str] = None
    ) -> AsyncGenerator[str, None]:
        """
        Gera resposta em streaming
        
        Args:
            prompt: Prompt principal
            context: Contexto adicional
            system_instruction: Instrução do sistema
            
        Yields:
            Chunks da resposta conforme são gerados
        """
        try:
            # Construir prompt completo
            full_prompt = self._build_prompt(prompt, context, system_instruction)
            
            logger.info(f"Iniciando streaming para prompt de {len(full_prompt)} caracteres")
            
            # Gerar resposta em streaming
            response = self.model.generate_content(full_prompt, stream=True)
            
            for chunk in response:
                if chunk.text:
                    yield chunk.text
                    
        except Exception as e:
            logger.error(f"Erro no streaming: {e}")
            yield f"Erro ao processar sua solicitação: {str(e)}"
    
    def _build_prompt(
        self, 
        prompt: str, 
        context: Optional[str] = None,
        system_instruction: Optional[str] = None
    ) -> str:
        """
        Constrói o prompt completo para o modelo
        
        Args:
            prompt: Prompt principal
            context: Contexto adicional
            system_instruction: Instrução do sistema
            
        Returns:
            Prompt completo formatado
        """
        parts = []
        
        # Adicionar instrução do sistema
        if system_instruction:
            parts.append(f"INSTRUÇÃO DO SISTEMA: {system_instruction}")
        
        # Adicionar contexto se disponível
        if context:
            parts.append(f"CONTEXTO RELEVANTE:\n{context}")
        
        # Adicionar prompt principal
        parts.append(f"PERGUNTA: {prompt}")
        
        # Adicionar instrução final
        parts.append(
            "RESPOSTA: Baseie sua resposta no contexto fornecido quando disponível. "
            "Se o contexto não contém informações suficientes, indique isso claramente. "
            "Seja preciso, informativo e útil."
        )
        
        return "\n\n".join(parts)
    
    def get_model_info(self) -> Dict[str, Any]:
        """
        Retorna informações sobre o modelo
        
        Returns:
            Dicionário com informações do modelo
        """
        try:
            model_info = genai.get_model(self.config.model_name)
            return {
                "name": model_info.name,
                "display_name": model_info.display_name,
                "description": model_info.description,
                "input_token_limit": model_info.input_token_limit,
                "output_token_limit": model_info.output_token_limit,
                "supported_generation_methods": model_info.supported_generation_methods,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p,
                "top_k": self.config.top_k,
                "max_output_tokens": self.config.max_output_tokens
            }
        except Exception as e:
            logger.error(f"Erro ao obter informações do modelo: {e}")
            return {
                "name": self.config.model_name,
                "error": str(e)
            }
    
    def test_connection(self) -> bool:
        """
        Testa a conexão com a API do Gemini
        
        Returns:
            True se a conexão estiver funcionando
        """
        try:
            response = self.model.generate_content("Teste de conexão. Responda apenas 'OK'.")
            return bool(response.text)
        except Exception as e:
            logger.error(f"Erro no teste de conexão: {e}")
            return False


class GeminiRAGClient(GeminiClient):
    """
    Cliente Gemini especializado para RAG (Retrieval-Augmented Generation)
    """
    
    def __init__(self, config: Optional[GeminiConfig] = None):
        super().__init__(config)
        
        # Instrução específica para RAG
        self.rag_system_instruction = (
            "Você é um assistente especializado em responder perguntas baseado em documentos fornecidos. "
            "Sempre cite as fontes quando possível e indique quando as informações não estão disponíveis "
            "no contexto fornecido. Seja preciso e objetivo em suas respostas."
        )
    
    def generate_rag_response(
        self, 
        query: str, 
        retrieved_documents: List[str],
        max_context_length: int = 6000
    ) -> str:
        """
        Gera resposta RAG baseada em documentos recuperados
        
        Args:
            query: Pergunta do usuário
            retrieved_documents: Lista de documentos recuperados
            max_context_length: Tamanho máximo do contexto
            
        Returns:
            Resposta baseada nos documentos
        """
        # Construir contexto a partir dos documentos
        context = self._build_context(retrieved_documents, max_context_length)
        
        # Gerar resposta
        return self.generate_response(
            prompt=query,
            context=context,
            system_instruction=self.rag_system_instruction
        )
    
    def _build_context(self, documents: List[str], max_length: int) -> str:
        """
        Constrói contexto a partir dos documentos recuperados
        
        Args:
            documents: Lista de documentos
            max_length: Tamanho máximo do contexto
            
        Returns:
            Contexto formatado
        """
        context_parts = []
        current_length = 0
        
        for i, doc in enumerate(documents):
            doc_header = f"DOCUMENTO {i+1}:\n"
            doc_content = f"{doc}\n\n"
            doc_full = doc_header + doc_content
            
            if current_length + len(doc_full) > max_length:
                break
                
            context_parts.append(doc_full)
            current_length += len(doc_full)
        
        return "".join(context_parts)

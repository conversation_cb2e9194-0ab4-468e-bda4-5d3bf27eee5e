"""
Sistema RAG (Retrieval-Augmented Generation) com busca híbrida
"""

from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import asyncio
from loguru import logger

from .hybrid_search import HybridSearchEngine, Document, SearchResult
from .gemini_client import GeminiRAGClient, GeminiConfig


@dataclass
class RAGConfig:
    """Configuração do sistema RAG"""
    # Configurações de busca
    max_retrieved_docs: int = 5
    min_relevance_score: float = 0.1
    search_type: str = "hybrid"  # 'semantic', 'lexical', 'hybrid'
    
    # Configurações de contexto
    max_context_length: int = 6000
    include_metadata: bool = True
    
    # Configurações do modelo
    gemini_config: Optional[GeminiConfig] = None


@dataclass
class RAGResponse:
    """Resposta do sistema RAG"""
    answer: str
    retrieved_documents: List[SearchResult]
    query: str
    context_used: str
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class RAGSystem:
    """
    Sistema RAG que combina busca híbrida com geração de respostas
    """
    
    def __init__(self, config: Optional[RAGConfig] = None):
        """
        Inicializa o sistema RAG
        
        Args:
            config: Configuração do sistema
        """
        self.config = config or RAGConfig()
        
        # Inicializar componentes
        self.search_engine = HybridSearchEngine()
        self.llm_client = GeminiRAGClient(self.config.gemini_config)
        
        logger.info("Sistema RAG inicializado")
    
    def add_documents(self, documents: List[Document]):
        """
        Adiciona documentos ao sistema
        
        Args:
            documents: Lista de documentos para indexar
        """
        logger.info(f"Adicionando {len(documents)} documentos ao sistema RAG")
        self.search_engine.add_documents(documents)
    
    def query(self, question: str, **kwargs) -> RAGResponse:
        """
        Processa uma consulta usando RAG
        
        Args:
            question: Pergunta do usuário
            **kwargs: Parâmetros adicionais para personalizar a busca
            
        Returns:
            Resposta RAG completa
        """
        logger.info(f"Processando consulta RAG: '{question}'")
        
        # Parâmetros de busca
        max_docs = kwargs.get('max_retrieved_docs', self.config.max_retrieved_docs)
        search_type = kwargs.get('search_type', self.config.search_type)
        min_score = kwargs.get('min_relevance_score', self.config.min_relevance_score)
        
        # 1. Recuperar documentos relevantes
        retrieved_docs = self._retrieve_documents(
            question, max_docs, search_type, min_score
        )
        
        # 2. Construir contexto
        context = self._build_context(retrieved_docs)
        
        # 3. Gerar resposta
        answer = self._generate_answer(question, context, retrieved_docs)
        
        # 4. Criar resposta RAG
        response = RAGResponse(
            answer=answer,
            retrieved_documents=retrieved_docs,
            query=question,
            context_used=context,
            metadata={
                'search_type': search_type,
                'num_retrieved': len(retrieved_docs),
                'context_length': len(context)
            }
        )
        
        logger.info(f"Consulta RAG processada com {len(retrieved_docs)} documentos")
        return response
    
    async def query_async(self, question: str, **kwargs) -> RAGResponse:
        """
        Versão assíncrona da consulta RAG
        
        Args:
            question: Pergunta do usuário
            **kwargs: Parâmetros adicionais
            
        Returns:
            Resposta RAG completa
        """
        logger.info(f"Processando consulta RAG assíncrona: '{question}'")
        
        # Parâmetros de busca
        max_docs = kwargs.get('max_retrieved_docs', self.config.max_retrieved_docs)
        search_type = kwargs.get('search_type', self.config.search_type)
        min_score = kwargs.get('min_relevance_score', self.config.min_relevance_score)
        
        # 1. Recuperar documentos relevantes
        retrieved_docs = self._retrieve_documents(
            question, max_docs, search_type, min_score
        )
        
        # 2. Construir contexto
        context = self._build_context(retrieved_docs)
        
        # 3. Gerar resposta assíncrona
        answer = await self._generate_answer_async(question, context, retrieved_docs)
        
        # 4. Criar resposta RAG
        response = RAGResponse(
            answer=answer,
            retrieved_documents=retrieved_docs,
            query=question,
            context_used=context,
            metadata={
                'search_type': search_type,
                'num_retrieved': len(retrieved_docs),
                'context_length': len(context)
            }
        )
        
        logger.info(f"Consulta RAG assíncrona processada com {len(retrieved_docs)} documentos")
        return response
    
    def _retrieve_documents(
        self, 
        query: str, 
        max_docs: int, 
        search_type: str,
        min_score: float
    ) -> List[SearchResult]:
        """
        Recupera documentos relevantes usando busca híbrida
        
        Args:
            query: Consulta de busca
            max_docs: Número máximo de documentos
            search_type: Tipo de busca
            min_score: Score mínimo de relevância
            
        Returns:
            Lista de documentos recuperados
        """
        try:
            # Realizar busca
            results = self.search_engine.search(
                query=query,
                top_k=max_docs,
                search_type=search_type
            )
            
            # Filtrar por score mínimo
            filtered_results = [
                result for result in results 
                if result.score >= min_score
            ]
            
            logger.info(
                f"Recuperados {len(filtered_results)} documentos "
                f"(de {len(results)} encontrados) com score >= {min_score}"
            )
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"Erro na recuperação de documentos: {e}")
            return []
    
    def _build_context(self, retrieved_docs: List[SearchResult]) -> str:
        """
        Constrói contexto a partir dos documentos recuperados
        
        Args:
            retrieved_docs: Documentos recuperados
            
        Returns:
            Contexto formatado
        """
        if not retrieved_docs:
            return "Nenhum documento relevante encontrado."
        
        context_parts = []
        current_length = 0
        max_length = self.config.max_context_length
        
        for i, result in enumerate(retrieved_docs):
            # Cabeçalho do documento
            header = f"DOCUMENTO {i+1} (Score: {result.score:.3f}):\n"
            
            # Conteúdo do documento
            content = result.document.content
            
            # Metadados (se habilitado)
            metadata_str = ""
            if self.config.include_metadata and result.document.metadata:
                metadata_items = [
                    f"{k}: {v}" for k, v in result.document.metadata.items()
                ]
                metadata_str = f"Metadados: {', '.join(metadata_items)}\n"
            
            # Documento completo
            doc_text = f"{header}{metadata_str}{content}\n\n"
            
            # Verificar limite de contexto
            if current_length + len(doc_text) > max_length:
                # Truncar se necessário
                remaining_space = max_length - current_length
                if remaining_space > 100:  # Mínimo útil
                    truncated_content = content[:remaining_space-len(header)-len(metadata_str)-20]
                    doc_text = f"{header}{metadata_str}{truncated_content}...\n\n"
                    context_parts.append(doc_text)
                break
            
            context_parts.append(doc_text)
            current_length += len(doc_text)
        
        return "".join(context_parts)
    
    def _generate_answer(
        self, 
        question: str, 
        context: str, 
        retrieved_docs: List[SearchResult]
    ) -> str:
        """
        Gera resposta usando o modelo LLM
        
        Args:
            question: Pergunta do usuário
            context: Contexto construído
            retrieved_docs: Documentos recuperados
            
        Returns:
            Resposta gerada
        """
        try:
            # Extrair apenas o conteúdo dos documentos para o cliente
            doc_contents = [result.document.content for result in retrieved_docs]
            
            # Gerar resposta RAG
            answer = self.llm_client.generate_rag_response(
                query=question,
                retrieved_documents=doc_contents,
                max_context_length=self.config.max_context_length
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Erro na geração de resposta: {e}")
            return f"Erro ao gerar resposta: {str(e)}"
    
    async def _generate_answer_async(
        self, 
        question: str, 
        context: str, 
        retrieved_docs: List[SearchResult]
    ) -> str:
        """
        Versão assíncrona da geração de resposta
        
        Args:
            question: Pergunta do usuário
            context: Contexto construído
            retrieved_docs: Documentos recuperados
            
        Returns:
            Resposta gerada
        """
        try:
            # Gerar resposta assíncrona
            answer = await self.llm_client.generate_response_async(
                prompt=question,
                context=context,
                system_instruction=self.llm_client.rag_system_instruction
            )
            
            return answer
            
        except Exception as e:
            logger.error(f"Erro na geração de resposta assíncrona: {e}")
            return f"Erro ao gerar resposta: {str(e)}"
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do sistema RAG
        
        Returns:
            Dicionário com estatísticas
        """
        search_stats = self.search_engine.get_stats()
        model_info = self.llm_client.get_model_info()
        
        return {
            "search_engine": search_stats,
            "llm_model": model_info,
            "config": {
                "max_retrieved_docs": self.config.max_retrieved_docs,
                "min_relevance_score": self.config.min_relevance_score,
                "search_type": self.config.search_type,
                "max_context_length": self.config.max_context_length,
                "include_metadata": self.config.include_metadata
            }
        }
    
    def test_system(self) -> Dict[str, bool]:
        """
        Testa todos os componentes do sistema
        
        Returns:
            Dicionário com status dos testes
        """
        results = {}
        
        # Testar conexão com LLM
        results["llm_connection"] = self.llm_client.test_connection()
        
        # Testar busca (se há documentos)
        if len(self.search_engine.documents) > 0:
            try:
                test_results = self.search_engine.search("teste", top_k=1)
                results["search_engine"] = len(test_results) >= 0
            except Exception:
                results["search_engine"] = False
        else:
            results["search_engine"] = True  # OK se não há documentos
        
        # Teste integrado (se possível)
        if results["llm_connection"] and results["search_engine"]:
            try:
                if len(self.search_engine.documents) > 0:
                    test_response = self.query("teste de funcionamento")
                    results["integrated_test"] = bool(test_response.answer)
                else:
                    results["integrated_test"] = True  # OK se não há documentos
            except Exception:
                results["integrated_test"] = False
        else:
            results["integrated_test"] = False
        
        return results

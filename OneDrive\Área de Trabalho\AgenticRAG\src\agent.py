"""
Agente Inteligente RAG usando framework AGNO e Gemini 2.0 Flash
"""

import os
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import asyncio
from dataclasses import dataclass

from agno.agent import Agent
from agno.models.google import Gemini
from agno.tools.reasoning import ReasoningTools
from loguru import logger

from .rag_system import RAGSystem, RAGConfig, RAGResponse
from .document_processor import DocumentProcessor, ProcessingConfig
from .hybrid_search import Document
from .gemini_client import GeminiConfig


@dataclass
class AgentConfig:
    """Configuração do agente inteligente"""
    # Configurações do RAG
    rag_config: Optional[RAGConfig] = None
    
    # Configurações do processamento
    processing_config: Optional[ProcessingConfig] = None
    
    # Configurações do agente AGNO
    agent_name: str = "Agente RAG Híbrido"
    agent_role: str = "Especialista em recuperação e análise de informações"
    temperature: float = 0.7
    use_reasoning: bool = True
    markdown_output: bool = True
    
    # Configurações do modelo
    model_name: str = "gemini-2.0-flash-exp"
    api_key: Optional[str] = None


class HybridRAGAgent:
    """
    Agente inteligente que combina AGNO, RAG e busca híbrida
    """
    
    def __init__(self, config: Optional[AgentConfig] = None):
        """
        Inicializa o agente RAG híbrido
        
        Args:
            config: Configuração do agente
        """
        self.config = config or AgentConfig()
        
        # Configurar API key
        api_key = self.config.api_key or os.getenv("GOOGLE_API_KEY")
        if not api_key:
            raise ValueError(
                "API key do Google não encontrada. "
                "Configure GOOGLE_API_KEY ou passe via config.api_key"
            )
        
        # Inicializar componentes
        self._initialize_components()
        
        # Inicializar agente AGNO
        self._initialize_agno_agent()
        
        logger.info(f"Agente {self.config.agent_name} inicializado com sucesso")
    
    def _initialize_components(self):
        """Inicializa os componentes do sistema"""
        # Configurar RAG
        if self.config.rag_config is None:
            gemini_config = GeminiConfig(
                model_name=self.config.model_name,
                temperature=self.config.temperature,
                api_key=self.config.api_key
            )
            self.config.rag_config = RAGConfig(gemini_config=gemini_config)
        
        # Inicializar sistema RAG
        self.rag_system = RAGSystem(self.config.rag_config)
        
        # Inicializar processador de documentos
        self.document_processor = DocumentProcessor(self.config.processing_config)
        
        logger.info("Componentes do sistema inicializados")
    
    def _initialize_agno_agent(self):
        """Inicializa o agente AGNO"""
        try:
            # Configurar modelo Gemini para AGNO
            model = Gemini(
                id=self.config.model_name,
                api_key=self.config.api_key or os.getenv("GOOGLE_API_KEY")
            )
            
            # Configurar ferramentas
            tools = []
            if self.config.use_reasoning:
                tools.append(ReasoningTools(add_instructions=True))
            
            # Instruções do agente
            instructions = [
                f"Você é o {self.config.agent_name}, um {self.config.agent_role}.",
                "Você tem acesso a um sistema RAG avançado com busca híbrida.",
                "Sempre use o contexto fornecido pelos documentos para responder perguntas.",
                "Se não houver informações suficientes nos documentos, indique isso claramente.",
                "Cite as fontes quando possível e seja preciso em suas respostas.",
                "Use raciocínio estruturado para problemas complexos.",
                "Formate suas respostas de forma clara e organizada."
            ]
            
            # Criar agente AGNO
            self.agno_agent = Agent(
                name=self.config.agent_name,
                role=self.config.agent_role,
                model=model,
                tools=tools,
                instructions=instructions,
                markdown=self.config.markdown_output,
                show_tool_calls=True
            )
            
            logger.info("Agente AGNO configurado com sucesso")
            
        except Exception as e:
            logger.error(f"Erro ao inicializar agente AGNO: {e}")
            raise
    
    def index_documents(
        self, 
        source: Union[str, Path, List[str]], 
        recursive: bool = True
    ) -> int:
        """
        Indexa documentos no sistema
        
        Args:
            source: Caminho para arquivo, diretório ou lista de caminhos
            recursive: Se deve processar subdiretórios
            
        Returns:
            Número de documentos indexados
        """
        logger.info(f"Iniciando indexação de documentos: {source}")
        
        all_documents = []
        
        if isinstance(source, (str, Path)):
            source_path = Path(source)
            
            if source_path.is_file():
                # Processar arquivo único
                documents = self.document_processor.process_file(source_path)
                all_documents.extend(documents)
            elif source_path.is_dir():
                # Processar diretório
                documents = self.document_processor.process_directory(
                    source_path, recursive=recursive
                )
                all_documents.extend(documents)
            else:
                logger.error(f"Caminho não encontrado: {source_path}")
                return 0
        
        elif isinstance(source, list):
            # Processar lista de caminhos
            for path in source:
                path_obj = Path(path)
                if path_obj.is_file():
                    documents = self.document_processor.process_file(path_obj)
                    all_documents.extend(documents)
                elif path_obj.is_dir():
                    documents = self.document_processor.process_directory(
                        path_obj, recursive=recursive
                    )
                    all_documents.extend(documents)
        
        # Adicionar documentos ao sistema RAG
        if all_documents:
            self.rag_system.add_documents(all_documents)
            logger.info(f"Indexação concluída: {len(all_documents)} documentos")
        else:
            logger.warning("Nenhum documento foi processado")
        
        return len(all_documents)
    
    def index_text(
        self, 
        text: str, 
        source_name: str = "text_input",
        metadata: Optional[Dict[str, Any]] = None
    ) -> int:
        """
        Indexa texto diretamente
        
        Args:
            text: Texto para indexar
            source_name: Nome da fonte
            metadata: Metadados adicionais
            
        Returns:
            Número de chunks criados
        """
        logger.info(f"Indexando texto: {len(text)} caracteres")
        
        documents = self.document_processor.process_text(
            text=text,
            source_name=source_name,
            metadata=metadata
        )
        
        if documents:
            self.rag_system.add_documents(documents)
            logger.info(f"Texto indexado: {len(documents)} chunks")
        
        return len(documents)
    
    def query(self, question: str, **kwargs) -> str:
        """
        Processa uma consulta usando RAG e o agente AGNO
        
        Args:
            question: Pergunta do usuário
            **kwargs: Parâmetros adicionais para o RAG
            
        Returns:
            Resposta do agente
        """
        logger.info(f"Processando consulta: '{question}'")
        
        try:
            # Obter resposta RAG
            rag_response = self.rag_system.query(question, **kwargs)
            
            # Construir prompt para o agente AGNO
            prompt = self._build_agno_prompt(question, rag_response)
            
            # Obter resposta do agente AGNO
            agent_response = self.agno_agent.run(prompt)
            
            logger.info("Consulta processada com sucesso")
            return agent_response.content
            
        except Exception as e:
            logger.error(f"Erro ao processar consulta: {e}")
            return f"Erro ao processar sua pergunta: {str(e)}"
    
    async def query_async(self, question: str, **kwargs) -> str:
        """
        Versão assíncrona da consulta
        
        Args:
            question: Pergunta do usuário
            **kwargs: Parâmetros adicionais
            
        Returns:
            Resposta do agente
        """
        logger.info(f"Processando consulta assíncrona: '{question}'")
        
        try:
            # Obter resposta RAG assíncrona
            rag_response = await self.rag_system.query_async(question, **kwargs)
            
            # Construir prompt para o agente AGNO
            prompt = self._build_agno_prompt(question, rag_response)
            
            # Obter resposta do agente AGNO (AGNO não tem versão async nativa)
            agent_response = self.agno_agent.run(prompt)
            
            logger.info("Consulta assíncrona processada com sucesso")
            return agent_response.content
            
        except Exception as e:
            logger.error(f"Erro ao processar consulta assíncrona: {e}")
            return f"Erro ao processar sua pergunta: {str(e)}"
    
    def _build_agno_prompt(self, question: str, rag_response: RAGResponse) -> str:
        """
        Constrói prompt para o agente AGNO baseado na resposta RAG
        
        Args:
            question: Pergunta original
            rag_response: Resposta do sistema RAG
            
        Returns:
            Prompt formatado
        """
        # Informações sobre documentos recuperados
        doc_info = []
        for i, result in enumerate(rag_response.retrieved_documents):
            doc_info.append(
                f"Documento {i+1} (Score: {result.score:.3f}): "
                f"{result.document.content[:200]}..."
            )
        
        prompt_parts = [
            f"PERGUNTA DO USUÁRIO: {question}",
            "",
            "CONTEXTO RECUPERADO:",
            rag_response.context_used,
            "",
            f"DOCUMENTOS ENCONTRADOS: {len(rag_response.retrieved_documents)}",
            "\n".join(doc_info),
            "",
            "INSTRUÇÕES:",
            "- Use o contexto fornecido para responder à pergunta",
            "- Se o contexto não for suficiente, indique isso claramente",
            "- Cite as fontes quando possível",
            "- Seja preciso e informativo",
            "- Use raciocínio estruturado se necessário"
        ]
        
        return "\n".join(prompt_parts)
    
    def get_stats(self) -> Dict[str, Any]:
        """
        Retorna estatísticas do agente
        
        Returns:
            Dicionário com estatísticas completas
        """
        rag_stats = self.rag_system.get_stats()
        
        return {
            "agent_config": {
                "name": self.config.agent_name,
                "role": self.config.agent_role,
                "model": self.config.model_name,
                "use_reasoning": self.config.use_reasoning
            },
            "rag_system": rag_stats,
            "processing_config": {
                "chunk_size": self.document_processor.config.chunk_size,
                "chunk_overlap": self.document_processor.config.chunk_overlap,
                "supported_extensions": self.document_processor.config.supported_extensions
            }
        }
    
    def test_system(self) -> Dict[str, bool]:
        """
        Testa todos os componentes do sistema
        
        Returns:
            Dicionário com status dos testes
        """
        logger.info("Executando testes do sistema")
        
        results = {}
        
        # Testar sistema RAG
        rag_tests = self.rag_system.test_system()
        results.update(rag_tests)
        
        # Testar agente AGNO
        try:
            test_response = self.agno_agent.run("Teste de funcionamento. Responda apenas 'OK'.")
            results["agno_agent"] = bool(test_response.content)
        except Exception as e:
            logger.error(f"Erro no teste do agente AGNO: {e}")
            results["agno_agent"] = False
        
        # Teste integrado completo
        if all(results.values()):
            try:
                if len(self.rag_system.search_engine.documents) > 0:
                    test_query = self.query("teste de funcionamento completo")
                    results["full_integration"] = bool(test_query)
                else:
                    results["full_integration"] = True  # OK se não há documentos
            except Exception as e:
                logger.error(f"Erro no teste integrado: {e}")
                results["full_integration"] = False
        else:
            results["full_integration"] = False
        
        logger.info(f"Testes concluídos: {results}")
        return results

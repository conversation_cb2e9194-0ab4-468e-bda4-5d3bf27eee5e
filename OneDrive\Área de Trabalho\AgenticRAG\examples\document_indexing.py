"""
Exemplo de indexação de documentos
"""

import os
from pathlib import Path
from src.agent import HybridRAGAgent


def create_sample_documents():
    """Cria documentos de exemplo para teste"""
    
    # Criar diretório de exemplo
    sample_dir = Path("data/sample_docs")
    sample_dir.mkdir(parents=True, exist_ok=True)
    
    # Documento 1: Sobre IA
    doc1_content = """
    # Inteligência Artificial: Uma Visão Geral
    
    A Inteligência Artificial (IA) representa uma das mais significativas revoluções 
    tecnológicas da era moderna. Desde seus primórdios na década de 1950, a IA tem 
    evoluído de conceitos teóricos para aplicações práticas que transformam indústrias 
    inteiras.
    
    ## Definição e Conceitos Fundamentais
    
    A IA pode ser definida como a capacidade de sistemas computacionais de realizar 
    tarefas que tradicionalmente requerem inteligência humana. Isso inclui:
    
    - Aprendizado e adaptação
    - Raciocínio lógico
    - Percepção sensorial
    - Compreensão de linguagem
    - Tomada de decisões
    
    ## Tipos de Inteligência Artificial
    
    ### IA Estreita (Narrow AI)
    Sistemas especializados em tarefas específicas, como reconhecimento de voz ou 
    recomendações de produtos.
    
    ### IA Geral (AGI)
    Sistemas hipotéticos com capacidades cognitivas equivalentes aos humanos em 
    todas as áreas.
    
    ### Super IA
    Sistemas que superariam a inteligência humana em todos os aspectos.
    """
    
    with open(sample_dir / "ia_overview.md", "w", encoding="utf-8") as f:
        f.write(doc1_content)
    
    # Documento 2: Machine Learning
    doc2_content = """
    # Machine Learning: Fundamentos e Aplicações
    
    Machine Learning (ML) é um subcampo da Inteligência Artificial que permite aos 
    computadores aprender e melhorar automaticamente através da experiência, sem 
    serem explicitamente programados para cada tarefa específica.
    
    ## Tipos de Aprendizado
    
    ### Aprendizado Supervisionado
    - Utiliza dados rotulados para treinamento
    - Exemplos: classificação de emails, reconhecimento de imagens
    - Algoritmos: Regressão Linear, SVM, Random Forest
    
    ### Aprendizado Não Supervisionado
    - Encontra padrões em dados não rotulados
    - Exemplos: clustering de clientes, detecção de anomalias
    - Algoritmos: K-means, DBSCAN, PCA
    
    ### Aprendizado por Reforço
    - Aprende através de interação com ambiente
    - Exemplos: jogos, robótica, sistemas de recomendação
    - Algoritmos: Q-learning, Policy Gradient
    
    ## Aplicações Práticas
    
    - **Saúde**: Diagnóstico médico, descoberta de medicamentos
    - **Finanças**: Detecção de fraudes, trading algorítmico
    - **Transporte**: Veículos autônomos, otimização de rotas
    - **Tecnologia**: Assistentes virtuais, tradução automática
    """
    
    with open(sample_dir / "machine_learning.md", "w", encoding="utf-8") as f:
        f.write(doc2_content)
    
    # Documento 3: Deep Learning
    doc3_content = """
    # Deep Learning: Redes Neurais Profundas
    
    Deep Learning é uma técnica de Machine Learning baseada em redes neurais 
    artificiais com múltiplas camadas (daí o termo "profundo"). Esta abordagem 
    tem revolucionado campos como visão computacional e processamento de 
    linguagem natural.
    
    ## Arquiteturas Principais
    
    ### Redes Neurais Convolucionais (CNN)
    - Especializadas em processamento de imagens
    - Utilizam operações de convolução
    - Aplicações: reconhecimento facial, diagnóstico médico por imagem
    
    ### Redes Neurais Recorrentes (RNN)
    - Processam sequências de dados
    - Mantêm memória de estados anteriores
    - Aplicações: tradução automática, análise de sentimentos
    
    ### Transformers
    - Arquitetura baseada em mecanismos de atenção
    - Estado da arte em NLP
    - Exemplos: GPT, BERT, T5
    
    ## Vantagens e Desafios
    
    ### Vantagens
    - Capacidade de aprender representações complexas
    - Performance superior em tarefas específicas
    - Automatização de extração de características
    
    ### Desafios
    - Necessidade de grandes volumes de dados
    - Alto custo computacional
    - Interpretabilidade limitada ("caixa preta")
    - Overfitting em datasets pequenos
    """
    
    with open(sample_dir / "deep_learning.md", "w", encoding="utf-8") as f:
        f.write(doc3_content)
    
    print(f"✅ Documentos de exemplo criados em: {sample_dir}")
    return sample_dir


def main():
    """Exemplo de indexação de documentos"""
    
    # Verificar API key
    if not os.getenv("GOOGLE_API_KEY"):
        print("❌ GOOGLE_API_KEY não configurada!")
        return
    
    print("🚀 Exemplo de Indexação de Documentos")
    print("=" * 50)
    
    try:
        # Criar documentos de exemplo
        print("\n📝 Criando documentos de exemplo...")
        sample_dir = create_sample_documents()
        
        # Inicializar agente
        print("\n🤖 Inicializando agente...")
        agent = HybridRAGAgent()
        
        # Indexar documentos
        print(f"\n📚 Indexando documentos do diretório: {sample_dir}")
        docs_indexados = agent.index_documents(sample_dir, recursive=True)
        print(f"✅ {docs_indexados} documentos indexados com sucesso!")
        
        # Mostrar estatísticas
        print("\n📊 Estatísticas após indexação:")
        stats = agent.get_stats()
        search_stats = stats['rag_system']['search_engine']
        
        print(f"  📄 Total de documentos: {search_stats['total_documents']}")
        print(f"  🧠 Modelo de embedding: {search_stats['embedding_model']}")
        print(f"  ⚖️  Peso semântico: {search_stats['semantic_weight']}")
        print(f"  ⚖️  Peso lexical: {search_stats['lexical_weight']}")
        
        # Testar diferentes tipos de busca
        print("\n🔍 Testando diferentes tipos de busca:")
        pergunta = "Quais são as principais arquiteturas de deep learning?"
        
        tipos_busca = ["semantic", "lexical", "hybrid"]
        
        for tipo in tipos_busca:
            print(f"\n--- Busca {tipo.upper()} ---")
            resposta = agent.query(pergunta, search_type=tipo, max_retrieved_docs=3)
            print(f"Resposta: {resposta[:200]}...")
        
        # Consultas específicas
        print("\n💬 Fazendo consultas específicas:")
        
        consultas = [
            "O que é aprendizado supervisionado?",
            "Quais são os desafios do deep learning?",
            "Como funcionam as redes neurais convolucionais?",
            "Qual a diferença entre IA estreita e IA geral?",
            "Quais são as aplicações de machine learning na saúde?"
        ]
        
        for i, consulta in enumerate(consultas, 1):
            print(f"\n{i}. {consulta}")
            resposta = agent.query(consulta)
            print(f"   Resposta: {resposta[:150]}...")
        
        # Demonstrar busca com parâmetros personalizados
        print("\n⚙️  Testando busca com parâmetros personalizados:")
        resposta_personalizada = agent.query(
            "Explique transformers em deep learning",
            max_retrieved_docs=2,
            search_type="hybrid",
            min_relevance_score=0.2
        )
        print(f"Resposta personalizada: {resposta_personalizada}")
        
        print("\n✅ Exemplo de indexação concluído com sucesso!")
        
    except Exception as e:
        print(f"❌ Erro durante execução: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

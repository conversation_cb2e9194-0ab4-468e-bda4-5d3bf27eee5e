"""
Testes para o sistema de busca híbrida
"""

import pytest
import numpy as np
from src.hybrid_search import HybridSearchEngine, Document, SearchResult


class TestHybridSearchEngine:
    """Testes para HybridSearchEngine"""
    
    @pytest.fixture
    def search_engine(self):
        """Fixture para criar uma instância do motor de busca"""
        return HybridSearchEngine(
            embedding_model="all-MiniLM-L6-v2",
            semantic_weight=0.6,
            lexical_weight=0.4
        )
    
    @pytest.fixture
    def sample_documents(self):
        """Fixture com documentos de exemplo"""
        return [
            Document(
                id="doc1",
                content="Inteligência artificial é uma área da ciência da computação",
                metadata={"tipo": "definição", "tema": "IA"}
            ),
            Document(
                id="doc2", 
                content="Machine learning é um subcampo da inteligência artificial",
                metadata={"tipo": "definição", "tema": "ML"}
            ),
            Document(
                id="doc3",
                content="Deep learning usa redes neurais com múltiplas camadas",
                metadata={"tipo": "definição", "tema": "DL"}
            ),
            Document(
                id="doc4",
                content="Python é uma linguagem de programação popular para IA",
                metadata={"tipo": "ferramenta", "tema": "programação"}
            )
        ]
    
    def test_initialization(self, search_engine):
        """Testa inicialização do motor de busca"""
        assert search_engine.embedding_model is not None
        assert search_engine.semantic_weight == 0.6
        assert search_engine.lexical_weight == 0.4
        assert len(search_engine.documents) == 0
    
    def test_add_documents(self, search_engine, sample_documents):
        """Testa adição de documentos"""
        search_engine.add_documents(sample_documents)
        
        assert len(search_engine.documents) == 4
        assert search_engine.faiss_index is not None
        assert search_engine.bm25_index is not None
        assert search_engine.document_embeddings is not None
    
    def test_semantic_search(self, search_engine, sample_documents):
        """Testa busca semântica"""
        search_engine.add_documents(sample_documents)
        
        results = search_engine.semantic_search("inteligência artificial", top_k=2)
        
        assert len(results) <= 2
        assert all(isinstance(r, SearchResult) for r in results)
        assert all(r.search_type == "semantic" for r in results)
        assert all(r.score >= 0 for r in results)
        
        # Verificar se está ordenado por score
        if len(results) > 1:
            assert results[0].score >= results[1].score
    
    def test_lexical_search(self, search_engine, sample_documents):
        """Testa busca lexical (BM25)"""
        search_engine.add_documents(sample_documents)
        
        results = search_engine.lexical_search("inteligência artificial", top_k=2)
        
        assert len(results) <= 2
        assert all(isinstance(r, SearchResult) for r in results)
        assert all(r.search_type == "lexical" for r in results)
        assert all(r.score >= 0 for r in results)
    
    def test_hybrid_search(self, search_engine, sample_documents):
        """Testa busca híbrida"""
        search_engine.add_documents(sample_documents)
        
        results = search_engine.hybrid_search("machine learning", top_k=3)
        
        assert len(results) <= 3
        assert all(isinstance(r, SearchResult) for r in results)
        assert all(r.search_type == "hybrid" for r in results)
        assert all(r.score >= 0 for r in results)
    
    def test_search_interface(self, search_engine, sample_documents):
        """Testa interface principal de busca"""
        search_engine.add_documents(sample_documents)
        
        # Testar diferentes tipos de busca
        semantic_results = search_engine.search("IA", search_type="semantic")
        lexical_results = search_engine.search("IA", search_type="lexical")
        hybrid_results = search_engine.search("IA", search_type="hybrid")
        
        assert all(r.search_type == "semantic" for r in semantic_results)
        assert all(r.search_type == "lexical" for r in lexical_results)
        assert all(r.search_type == "hybrid" for r in hybrid_results)
    
    def test_search_invalid_type(self, search_engine, sample_documents):
        """Testa busca com tipo inválido"""
        search_engine.add_documents(sample_documents)
        
        with pytest.raises(ValueError):
            search_engine.search("teste", search_type="invalid")
    
    def test_empty_search(self, search_engine):
        """Testa busca sem documentos indexados"""
        results = search_engine.search("qualquer coisa")
        assert len(results) == 0
    
    def test_get_stats(self, search_engine, sample_documents):
        """Testa obtenção de estatísticas"""
        # Antes de adicionar documentos
        stats_empty = search_engine.get_stats()
        assert stats_empty["total_documents"] == 0
        
        # Depois de adicionar documentos
        search_engine.add_documents(sample_documents)
        stats_full = search_engine.get_stats()
        
        assert stats_full["total_documents"] == 4
        assert stats_full["embedding_model"] == "all-MiniLM-L6-v2"
        assert stats_full["semantic_index_size"] == 4
        assert stats_full["bm25_configured"] is True
    
    def test_document_metadata(self, search_engine, sample_documents):
        """Testa preservação de metadados"""
        search_engine.add_documents(sample_documents)
        
        results = search_engine.search("inteligência artificial")
        
        for result in results:
            assert result.document.metadata is not None
            assert "tipo" in result.document.metadata
            assert "tema" in result.document.metadata
    
    def test_multiple_additions(self, search_engine, sample_documents):
        """Testa múltiplas adições de documentos"""
        # Primeira adição
        search_engine.add_documents(sample_documents[:2])
        assert len(search_engine.documents) == 2
        
        # Segunda adição
        search_engine.add_documents(sample_documents[2:])
        assert len(search_engine.documents) == 4
        
        # Verificar se busca funciona
        results = search_engine.search("inteligência")
        assert len(results) > 0


class TestDocument:
    """Testes para a classe Document"""
    
    def test_document_creation(self):
        """Testa criação de documento"""
        doc = Document(
            id="test_doc",
            content="Conteúdo de teste",
            metadata={"autor": "teste"}
        )
        
        assert doc.id == "test_doc"
        assert doc.content == "Conteúdo de teste"
        assert doc.metadata["autor"] == "teste"
    
    def test_document_without_metadata(self):
        """Testa documento sem metadados"""
        doc = Document(id="test", content="teste")
        
        assert doc.metadata == {}
    
    def test_document_metadata_default(self):
        """Testa inicialização padrão de metadados"""
        doc = Document(id="test", content="teste", metadata=None)
        
        assert doc.metadata == {}


class TestSearchResult:
    """Testes para a classe SearchResult"""
    
    def test_search_result_creation(self):
        """Testa criação de resultado de busca"""
        doc = Document(id="test", content="teste")
        result = SearchResult(
            document=doc,
            score=0.85,
            search_type="semantic"
        )
        
        assert result.document == doc
        assert result.score == 0.85
        assert result.search_type == "semantic"

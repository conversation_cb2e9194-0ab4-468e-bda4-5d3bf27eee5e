"""
Script para executar testes do Agente RAG Híbrido
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(command, description):
    """Executa um comando e mostra o resultado"""
    print(f"\n{'='*60}")
    print(f"🔍 {description}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(
            command,
            shell=True,
            capture_output=True,
            text=True,
            cwd=Path(__file__).parent
        )
        
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - SUCESSO")
        else:
            print(f"❌ {description} - FALHOU (código: {result.returncode})")
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ Erro ao executar {description}: {e}")
        return False


def check_dependencies():
    """Verifica se as dependências estão instaladas"""
    print("🔍 Verificando dependências...")
    
    required_packages = [
        "pytest",
        "pytest-asyncio",
        "sentence-transformers",
        "faiss-cpu",
        "rank-bm25",
        "google-generativeai",
        "agno"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - NÃO ENCONTRADO")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Pacotes faltando: {', '.join(missing_packages)}")
        print("Execute: pip install -r requirements.txt")
        return False
    
    return True


def main():
    """Função principal"""
    print("🚀 Executando Testes do Agente RAG Híbrido")
    print("=" * 60)
    
    # Verificar se estamos no diretório correto
    if not Path("src").exists():
        print("❌ Diretório 'src' não encontrado. Execute este script na raiz do projeto.")
        return 1
    
    # Verificar dependências
    if not check_dependencies():
        return 1
    
    # Verificar se pytest está disponível
    try:
        import pytest
        print(f"✅ pytest versão: {pytest.__version__}")
    except ImportError:
        print("❌ pytest não está instalado. Execute: pip install pytest pytest-asyncio")
        return 1
    
    # Configurar variável de ambiente para testes
    os.environ["TESTING"] = "true"
    
    # Lista de comandos de teste
    test_commands = [
        # Testes unitários básicos
        ("pytest tests/test_hybrid_search.py -v", "Testes de Busca Híbrida"),
        
        # Testes do sistema RAG
        ("pytest tests/test_rag_system.py -v", "Testes do Sistema RAG"),
        
        # Testes do agente
        ("pytest tests/test_agent.py -v", "Testes do Agente"),
        
        # Todos os testes com cobertura (se disponível)
        ("pytest tests/ -v --tb=short", "Todos os Testes"),
        
        # Testes com relatório detalhado
        ("pytest tests/ -v --tb=long --durations=10", "Relatório Detalhado"),
    ]
    
    # Executar testes
    success_count = 0
    total_count = len(test_commands)
    
    for command, description in test_commands:
        success = run_command(command, description)
        if success:
            success_count += 1
    
    # Resumo final
    print(f"\n{'='*60}")
    print("📊 RESUMO DOS TESTES")
    print(f"{'='*60}")
    print(f"✅ Sucessos: {success_count}/{total_count}")
    print(f"❌ Falhas: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("\n🎉 TODOS OS TESTES PASSARAM!")
        return 0
    else:
        print(f"\n⚠️  {total_count - success_count} TESTE(S) FALHARAM")
        return 1


if __name__ == "__main__":
    sys.exit(main())

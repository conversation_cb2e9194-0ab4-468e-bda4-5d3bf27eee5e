# Guia de Uso

## Instalação

### 1. Pré-requisitos

- Python 3.8 ou superior
- <PERSON>ve da <PERSON> do Google Gemini
- 4GB+ de RAM (recomendado)

### 2. Configuração do Ambiente

```bash
# Clonar o repositório
git clone <repository-url>
cd AgenticRAG

# Criar ambiente virtual
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou
venv\Scripts\activate  # Windows

# Instalar dependências
pip install -r requirements.txt
```

### 3. Configuração das Variáveis de Ambiente

```bash
# Copiar arquivo de exemplo
cp .env.example .env

# Editar .env com suas configurações
GOOGLE_API_KEY=sua_chave_api_aqui
```

## Uso Básico

### 1. Uso Programático

```python
from src.agent import HybridRAGAgent

# Inicializar o agente
agent = HybridRAGAgent()

# Indexar documentos de um diretório
agent.index_documents("caminho/para/documentos/")

# Fazer uma consulta
resposta = agent.query("Qual é o tema principal dos documentos?")
print(resposta)
```

### 2. Indexação de Texto Direto

```python
# Indexar texto diretamente
texto = """
Este é um exemplo de texto que será indexado.
O sistema irá dividir este texto em chunks menores
para melhor recuperação durante as consultas.
"""

agent.index_text(
    text=texto,
    source_name="exemplo_texto",
    metadata={"tipo": "exemplo", "autor": "sistema"}
)
```

### 3. Configuração Personalizada

```python
from src.agent import HybridRAGAgent, AgentConfig
from src.rag_system import RAGConfig
from src.gemini_client import GeminiConfig

# Configurar Gemini
gemini_config = GeminiConfig(
    model_name="gemini-2.0-flash-exp",
    temperature=0.5,  # Menos criativo
    max_output_tokens=4096
)

# Configurar RAG
rag_config = RAGConfig(
    max_retrieved_docs=10,  # Mais documentos
    search_type="hybrid",
    gemini_config=gemini_config
)

# Configurar agente
agent_config = AgentConfig(
    rag_config=rag_config,
    agent_name="Meu Agente Personalizado",
    use_reasoning=True
)

# Criar agente
agent = HybridRAGAgent(agent_config)
```

## Uso via API

### 1. Iniciar o Servidor

```bash
# Método 1: Usando uvicorn diretamente
uvicorn src.api:app --host 0.0.0.0 --port 8000 --reload

# Método 2: Usando o script Python
python -m src.api
```

### 2. Endpoints Principais

#### Verificar Saúde
```bash
curl http://localhost:8000/health
```

#### Indexar Texto
```bash
curl -X POST "http://localhost:8000/index/text" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Texto para indexar",
    "source_name": "exemplo",
    "metadata": {"tipo": "teste"}
  }'
```

#### Fazer Consulta
```bash
curl -X POST "http://localhost:8000/query" \
  -H "Content-Type: application/json" \
  -d '{
    "question": "Qual é o conteúdo indexado?",
    "max_retrieved_docs": 5,
    "search_type": "hybrid"
  }'
```

#### Upload de Arquivos
```bash
curl -X POST "http://localhost:8000/index/files" \
  -F "files=@documento.pdf" \
  -F "files=@outro_documento.txt"
```

### 3. Usando Python com requests

```python
import requests

# Configurar base URL
base_url = "http://localhost:8000"

# Indexar texto
response = requests.post(f"{base_url}/index/text", json={
    "text": "Conteúdo para indexar",
    "source_name": "api_test"
})
print(response.json())

# Fazer consulta
response = requests.post(f"{base_url}/query", json={
    "question": "O que foi indexado?",
    "search_type": "hybrid"
})
print(response.json())
```

## Tipos de Busca

### 1. Busca Semântica
Usa embeddings para encontrar documentos semanticamente similares:

```python
resposta = agent.query(
    "conceitos relacionados à inteligência artificial",
    search_type="semantic"
)
```

### 2. Busca Lexical
Usa BM25 para encontrar documentos com palavras-chave específicas:

```python
resposta = agent.query(
    "machine learning algoritmos",
    search_type="lexical"
)
```

### 3. Busca Híbrida (Recomendada)
Combina ambos os métodos para melhor precisão:

```python
resposta = agent.query(
    "como funciona deep learning?",
    search_type="hybrid"  # Padrão
)
```

## Formatos de Documento Suportados

- **PDF**: Extração de texto usando PyPDF2
- **DOCX**: Documentos Word usando python-docx
- **TXT/MD**: Arquivos de texto simples
- **CSV**: Planilhas CSV usando pandas
- **XLSX**: Planilhas Excel usando openpyxl

## Configurações Avançadas

### 1. Ajustar Pesos da Busca Híbrida

```python
from src.hybrid_search import HybridSearchEngine

# Criar engine personalizado
search_engine = HybridSearchEngine(
    semantic_weight=0.7,  # Mais peso semântico
    lexical_weight=0.3    # Menos peso lexical
)
```

### 2. Configurar Chunking

```python
from src.document_processor import ProcessingConfig

config = ProcessingConfig(
    chunk_size=1500,      # Chunks maiores
    chunk_overlap=300,    # Mais sobreposição
    min_chunk_size=200    # Chunks mínimos maiores
)
```

### 3. Ajustar Parâmetros do Modelo

```python
from src.gemini_client import GeminiConfig

config = GeminiConfig(
    temperature=0.3,      # Mais determinístico
    top_p=0.9,           # Menos diversidade
    max_output_tokens=2048  # Respostas mais curtas
)
```

## Monitoramento e Debugging

### 1. Verificar Estatísticas

```python
stats = agent.get_stats()
print(f"Documentos indexados: {stats['search_engine']['total_documents']}")
print(f"Modelo usado: {stats['llm_model']['name']}")
```

### 2. Testar Sistema

```python
testes = agent.test_system()
print("Status dos componentes:")
for componente, status in testes.items():
    print(f"  {componente}: {'✓' if status else '✗'}")
```

### 3. Logs Detalhados

```python
from loguru import logger

# Configurar nível de log
logger.remove()
logger.add("app.log", level="DEBUG")
logger.add(lambda msg: print(msg), level="INFO")
```

## Exemplos de Casos de Uso

### 1. Base de Conhecimento Corporativa

```python
# Indexar documentos da empresa
agent.index_documents("documentos_empresa/", recursive=True)

# Consultas típicas
resposta = agent.query("Qual é a política de férias?")
resposta = agent.query("Como solicitar reembolso de despesas?")
```

### 2. Análise de Documentos Técnicos

```python
# Indexar manuais técnicos
agent.index_documents("manuais_tecnicos/")

# Consultas técnicas
resposta = agent.query("Como configurar o sistema de backup?")
resposta = agent.query("Quais são os requisitos de hardware?")
```

### 3. Pesquisa Acadêmica

```python
# Indexar papers e artigos
agent.index_documents("papers/", recursive=True)

# Consultas de pesquisa
resposta = agent.query("Quais são as últimas tendências em NLP?")
resposta = agent.query("Compare diferentes arquiteturas de transformers")
```

## Solução de Problemas

### 1. Erro de API Key
```
ValueError: API key do Google não encontrada
```
**Solução**: Configurar `GOOGLE_API_KEY` no arquivo `.env`

### 2. Memória Insuficiente
```
MemoryError: Unable to allocate array
```
**Solução**: Reduzir `chunk_size` ou processar documentos em lotes menores

### 3. Nenhum Documento Encontrado
```
Nenhum documento relevante encontrado
```
**Solução**: Verificar se documentos foram indexados e ajustar `min_relevance_score`

### 4. Resposta Vazia do Modelo
```
Desculpe, não consegui gerar uma resposta adequada
```
**Solução**: Verificar conectividade com API Gemini e créditos disponíveis

# Arquitetura do Sistema

## Visão Geral

O Agente RAG Híbrido é um sistema inteligente que combina três tecnologias principais:

1. **Framework AGNO**: Para criação de agentes inteligentes com raciocínio
2. **Busca Híbrida**: Combinação de busca semântica e lexical
3. **Gemini 2.0 Flash**: Modelo de linguagem de última geração do Google

## Componentes Principais

### 1. HybridSearchEngine (`hybrid_search.py`)

Responsável pela busca híbrida que combina:

- **Busca Semântica**: Usa embeddings (SentenceTransformers) e FAISS para similaridade vetorial
- **Busca Lexical**: Implementa BM25 para busca por palavras-chave
- **Busca Híbrida**: Combina os dois métodos com pesos configuráveis

```python
# Configuração padrão
semantic_weight = 0.6  # 60% peso semântico
lexical_weight = 0.4   # 40% peso lexical
```

### 2. GeminiClient (`gemini_client.py`)

Cliente para integração com Google Gemini 2.0 Flash:

- **GeminiClient**: Cliente base para interação com a API
- **GeminiRAGClient**: Especializado para RAG com formatação de contexto
- Suporte a streaming e operações assíncronas

### 3. RAGSystem (`rag_system.py`)

Pipeline RAG que integra busca e geração:

1. **Recuperação**: Usa HybridSearchEngine para encontrar documentos relevantes
2. **Contextualização**: Formata documentos recuperados em contexto estruturado
3. **Geração**: Usa Gemini para gerar resposta baseada no contexto

### 4. DocumentProcessor (`document_processor.py`)

Processamento e indexação de documentos:

- **Extração**: Suporte a PDF, DOCX, TXT, CSV, XLSX, MD
- **Chunking**: Divisão inteligente em chunks com overlap
- **Metadados**: Extração automática de informações do arquivo

### 5. HybridRAGAgent (`agent.py`)

Agente principal que integra AGNO com RAG:

- **Agente AGNO**: Configurado com ReasoningTools
- **Integração RAG**: Usa sistema RAG para recuperar contexto
- **Prompt Engineering**: Constrói prompts otimizados para o modelo

### 6. API FastAPI (`api.py`)

Interface REST para interação:

- **Endpoints de Consulta**: `/query` para perguntas
- **Endpoints de Indexação**: `/index/text`, `/index/files`, `/index/directory`
- **Monitoramento**: `/health`, `/stats`

## Fluxo de Dados

```mermaid
graph TD
    A[Usuário] --> B[API FastAPI]
    B --> C[HybridRAGAgent]
    C --> D[RAGSystem]
    D --> E[HybridSearchEngine]
    E --> F[Documentos Indexados]
    E --> G[Busca Semântica FAISS]
    E --> H[Busca Lexical BM25]
    G --> I[Resultados Combinados]
    H --> I
    I --> D
    D --> J[GeminiClient]
    J --> K[Gemini 2.0 Flash]
    K --> L[Resposta Gerada]
    L --> C
    C --> M[Agente AGNO]
    M --> N[Resposta Final]
    N --> B
    B --> A
```

## Processo de Indexação

1. **Upload/Carregamento**: Documentos são carregados via API ou diretório
2. **Processamento**: DocumentProcessor extrai texto e cria chunks
3. **Embedding**: SentenceTransformers gera embeddings para cada chunk
4. **Indexação FAISS**: Embeddings são indexados para busca semântica
5. **Indexação BM25**: Texto é tokenizado e indexado para busca lexical
6. **Armazenamento**: Documentos e metadados são armazenados em memória

## Processo de Consulta

1. **Recepção**: API recebe pergunta do usuário
2. **Busca Híbrida**: 
   - Busca semântica usando embedding da pergunta
   - Busca lexical usando tokens da pergunta
   - Combinação dos resultados com pesos
3. **Contextualização**: Documentos relevantes são formatados em contexto
4. **Geração RAG**: Gemini gera resposta baseada no contexto
5. **Raciocínio AGNO**: Agente AGNO processa e refina a resposta
6. **Retorno**: Resposta final é enviada ao usuário

## Configurações Principais

### Busca Híbrida
- `semantic_weight`: Peso da busca semântica (padrão: 0.6)
- `lexical_weight`: Peso da busca lexical (padrão: 0.4)
- `max_retrieved_docs`: Máximo de documentos recuperados (padrão: 5)
- `min_relevance_score`: Score mínimo de relevância (padrão: 0.1)

### Processamento de Documentos
- `chunk_size`: Tamanho dos chunks (padrão: 1000 caracteres)
- `chunk_overlap`: Sobreposição entre chunks (padrão: 200 caracteres)
- `max_context_length`: Tamanho máximo do contexto (padrão: 6000 caracteres)

### Modelo Gemini
- `model_name`: Nome do modelo (padrão: "gemini-2.0-flash-exp")
- `temperature`: Criatividade do modelo (padrão: 0.7)
- `max_output_tokens`: Máximo de tokens na resposta (padrão: 8192)

## Vantagens da Arquitetura

1. **Busca Híbrida**: Combina precisão semântica com recall lexical
2. **Modularidade**: Componentes independentes e testáveis
3. **Escalabilidade**: FAISS permite indexação eficiente de grandes volumes
4. **Flexibilidade**: Configurações ajustáveis para diferentes casos de uso
5. **Raciocínio**: AGNO adiciona capacidades de raciocínio estruturado
6. **Performance**: Gemini 2.0 Flash oferece respostas rápidas e precisas

## Limitações e Considerações

1. **Memória**: Índices são mantidos em memória (adequado para volumes médios)
2. **Persistência**: Não há persistência automática dos índices
3. **Concorrência**: Não otimizado para alta concorrência
4. **Custos**: Uso da API Gemini gera custos por token
5. **Idioma**: Otimizado para português brasileiro

## Extensibilidade

O sistema foi projetado para ser extensível:

- **Novos Formatos**: Adicionar suporte a novos tipos de documento
- **Outros Modelos**: Integrar outros LLMs além do Gemini
- **Persistência**: Adicionar backends de armazenamento persistente
- **Métricas**: Implementar logging e métricas detalhadas
- **Cache**: Adicionar cache para consultas frequentes
